globalThis.process??={},globalThis.process.env??={};const ASTRO_VERSION="5.12.4",REROUTE_DIRECTIVE_HEADER="X-Astro-Reroute",REWRITE_DIRECTIVE_HEADER_KEY="X-Astro-Rewrite",REWRITE_DIRECTIVE_HEADER_VALUE="yes",NOOP_MIDDLEWARE_HEADER="X-Astro-Noop",ROUTE_TYPE_HEADER="X-Astro-Route-Type",DEFAULT_404_COMPONENT="astro-default-404.astro",REDIRECT_STATUS_CODES=[301,302,303,307,308,300,304],REROUTABLE_STATUS_CODES=[404,500],clientAddressSymbol=Symbol.for("astro.clientAddress"),originPathnameSymbol=Symbol.for("astro.originPathname"),responseSentSymbol=Symbol.for("astro.responseSent");function normalizeLF(e){return e.replace(/\r\n|\r(?!\n)|\n/g,"\n")}function codeFrame(e,t){if(!t||void 0===t.line||void 0===t.column)return"";const r=normalizeLF(e).split("\n").map((e=>e.replace(/\t/g,"  "))),n=[];for(let e=-2;e<=2;e++)r[t.line+e]&&n.push(t.line+e);let s=0;for(const e of n){let t=`> ${e}`;t.length>s&&(s=t.length)}let a="";for(const e of n){const n=e===t.line-1;a+=n?"> ":"  ",a+=`${e+1} | ${r[e]}\n`,n&&(a+=`${Array.from({length:s}).join(" ")}  | ${Array.from({length:t.column}).join(" ")}^\n`)}return a}class AstroError extends Error{loc;title;hint;frame;type="AstroError";constructor(e,t){const{name:r,title:n,message:s,stack:a,location:o,hint:i,frame:d}=e;super(s,t),this.title=n,this.name=r,s&&(this.message=s),this.stack=a||this.stack,this.loc=o,this.hint=i,this.frame=d}setLocation(e){this.loc=e}setName(e){this.name=e}setMessage(e){this.message=e}setHint(e){this.hint=e}setFrame(e,t){this.frame=codeFrame(e,t)}static is(e){return"AstroError"===e.type}}const ClientAddressNotAvailable={name:"ClientAddressNotAvailable",title:"`Astro.clientAddress` is not available in current adapter.",message:e=>`\`Astro.clientAddress\` is not available in the \`${e}\` adapter. File an issue with the adapter to add support.`},PrerenderClientAddressNotAvailable={name:"PrerenderClientAddressNotAvailable",title:"`Astro.clientAddress` cannot be used inside prerendered routes.",message:e=>`\`Astro.clientAddress\` cannot be used inside prerendered route ${e}`},StaticClientAddressNotAvailable={name:"StaticClientAddressNotAvailable",title:"`Astro.clientAddress` is not available in prerendered pages.",message:"`Astro.clientAddress` is only available on pages that are server-rendered.",hint:"See https://docs.astro.build/en/guides/on-demand-rendering/ for more information on how to enable SSR."},NoMatchingStaticPathFound={name:"NoMatchingStaticPathFound",title:"No static path found for requested path.",message:e=>`A \`getStaticPaths()\` route pattern was matched, but no matching static path was found for requested path \`${e}\`.`,hint:e=>`Possible dynamic routes being matched: ${e.join(", ")}.`},OnlyResponseCanBeReturned={name:"OnlyResponseCanBeReturned",title:"Invalid type returned by Astro page.",message:(e,t)=>`Route \`${e||""}\` returned a \`${t}\`. Only a [Response](https://developer.mozilla.org/en-US/docs/Web/API/Response) can be returned from Astro files.`,hint:"See https://docs.astro.build/en/guides/on-demand-rendering/#response for more information."},MissingMediaQueryDirective={name:"MissingMediaQueryDirective",title:"Missing value for `client:media` directive.",message:'Media query not provided for `client:media` directive. A media query similar to `client:media="(max-width: 600px)"` must be provided'},NoMatchingRenderer={name:"NoMatchingRenderer",title:"No matching renderer found.",message:(e,t,r,n)=>`Unable to render \`${e}\`.\n\n${n>0?`There ${r?"are":"is"} ${n} renderer${r?"s":""} configured in your \`astro.config.mjs\` file,\nbut ${r?"none were":"it was not"} able to server-side render \`${e}\`.`:"No valid renderer was found "+(t?`for the \`.${t}\` file extension.`:"for this file extension.")}`,hint:e=>`Did you mean to enable the ${e} integration?\n\nSee https://docs.astro.build/en/guides/framework-components/ for more information on how to install and configure integrations.`},NoClientOnlyHint={name:"NoClientOnlyHint",title:"Missing hint on client:only directive.",message:e=>`Unable to render \`${e}\`. When using the \`client:only\` hydration strategy, Astro needs a hint to use the correct renderer.`,hint:e=>`Did you mean to pass \`client:only="${e}"\`? See https://docs.astro.build/en/reference/directives-reference/#clientonly for more information on client:only`},InvalidGetStaticPathsEntry={name:"InvalidGetStaticPathsEntry",title:"Invalid entry inside getStaticPath's return value",message:e=>`Invalid entry returned by getStaticPaths. Expected an object, got \`${e}\``,hint:"If you're using a `.map` call, you might be looking for `.flatMap()` instead. See https://docs.astro.build/en/reference/routing-reference/#getstaticpaths for more information on getStaticPaths."},InvalidGetStaticPathsReturn={name:"InvalidGetStaticPathsReturn",title:"Invalid value returned by getStaticPaths.",message:e=>`Invalid type returned by \`getStaticPaths\`. Expected an \`array\`, got \`${e}\``,hint:"See https://docs.astro.build/en/reference/routing-reference/#getstaticpaths for more information on getStaticPaths."},GetStaticPathsExpectedParams={name:"GetStaticPathsExpectedParams",title:"Missing params property on `getStaticPaths` route.",message:"Missing or empty required `params` property on `getStaticPaths` route.",hint:"See https://docs.astro.build/en/reference/routing-reference/#getstaticpaths for more information on getStaticPaths."},GetStaticPathsInvalidRouteParam={name:"GetStaticPathsInvalidRouteParam",title:"Invalid value for `getStaticPaths` route parameter.",message:(e,t,r)=>`Invalid getStaticPaths route parameter for \`${e}\`. Expected undefined, a string or a number, received \`${r}\` (\`${t}\`)`,hint:"See https://docs.astro.build/en/reference/routing-reference/#getstaticpaths for more information on getStaticPaths."},GetStaticPathsRequired={name:"GetStaticPathsRequired",title:"`getStaticPaths()` function required for dynamic routes.",message:"`getStaticPaths()` function is required for dynamic routes. Make sure that you `export` a `getStaticPaths` function from your dynamic route.",hint:"See https://docs.astro.build/en/guides/routing/#dynamic-routes for more information on dynamic routes.\n\n\tIf you meant for this route to be server-rendered, set `export const prerender = false;` in the page."},ReservedSlotName={name:"ReservedSlotName",title:"Invalid slot name.",message:e=>`Unable to create a slot named \`${e}\`. \`${e}\` is a reserved slot name. Please update the name of this slot.`},NoMatchingImport={name:"NoMatchingImport",title:"No import found for component.",message:e=>`Could not render \`${e}\`. No matching import has been found for \`${e}\`.`,hint:"Please make sure the component is properly imported."},InvalidComponentArgs={name:"InvalidComponentArgs",title:"Invalid component arguments.",message:e=>`Invalid arguments passed to${e?` <${e}>`:""} component.`,hint:"Astro components cannot be rendered directly via function call, such as `Component()` or `{items.map(Component)}`."},PageNumberParamNotFound={name:"PageNumberParamNotFound",title:"Page number param not found.",message:e=>`[paginate()] page number param \`${e}\` not found in your filepath.`,hint:"Rename your file to `[page].astro` or `[...page].astro`."},ImageMissingAlt={name:"ImageMissingAlt",title:'Image missing required "alt" property.',message:'Image missing "alt" property. "alt" text is required to describe important images on the page.',hint:'Use an empty string ("") for decorative images.'},InvalidImageService={name:"InvalidImageService",title:"Error while loading image service.",message:"There was an error loading the configured image service. Please see the stack trace for more information."},MissingImageDimension={name:"MissingImageDimension",title:"Missing image dimensions",message:(e,t)=>`Missing ${"both"===e?"width and height attributes":`${e} attribute`} for ${t}. When using remote images, both dimensions are required in order to avoid CLS.`,hint:"If your image is inside your `src` folder, you probably meant to import it instead. See [the Imports guide for more information](https://docs.astro.build/en/guides/imports/#other-assets). You can also use `inferSize={true}` for remote images to get the original dimensions."},FailedToFetchRemoteImageDimensions={name:"FailedToFetchRemoteImageDimensions",title:"Failed to retrieve remote image dimensions",message:e=>`Failed to get the dimensions for ${e}.`,hint:"Verify your remote image URL is accurate, and that you are not using `inferSize` with a file located in your `public/` folder."},UnsupportedImageFormat={name:"UnsupportedImageFormat",title:"Unsupported image format",message:(e,t,r)=>`Received unsupported format \`${e}\` from \`${t}\`. Currently only ${r.join(", ")} are supported by our image services.`,hint:"Using an `img` tag directly instead of the `Image` component might be what you're looking for."},UnsupportedImageConversion={name:"UnsupportedImageConversion",title:"Unsupported image conversion",message:"Converting between vector (such as SVGs) and raster (such as PNGs and JPEGs) images is not currently supported."},PrerenderDynamicEndpointPathCollide={name:"PrerenderDynamicEndpointPathCollide",title:"Prerendered dynamic endpoint has path collision.",message:e=>`Could not render \`${e}\` with an \`undefined\` param as the generated path will collide during prerendering. Prevent passing \`undefined\` as \`params\` for the endpoint's \`getStaticPaths()\` function, or add an additional extension to the endpoint's filename.`,hint:e=>`Rename \`${e}\` to \`${e.replace(/\.(?:js|ts)/,(e=>".json"+e))}\``},ExpectedImage={name:"ExpectedImage",title:"Expected src to be an image.",message:(e,t,r)=>`Expected \`src\` property for \`getImage\` or \`<Image />\` to be either an ESM imported image or a string with the path of a remote image. Received \`${e}\` (type: \`${t}\`).\n\nFull serialized options received: \`${r}\`.`,hint:"This error can often happen because of a wrong path. Make sure the path to your image is correct. If you're passing an async function, make sure to call and await it."},ExpectedImageOptions={name:"ExpectedImageOptions",title:"Expected image options.",message:e=>`Expected getImage() parameter to be an object. Received \`${e}\`.`},ExpectedNotESMImage={name:"ExpectedNotESMImage",title:"Expected image options, not an ESM-imported image.",message:"An ESM-imported image cannot be passed directly to `getImage()`. Instead, pass an object with the image in the `src` property.",hint:"Try changing `getImage(myImage)` to `getImage({ src: myImage })`"},IncompatibleDescriptorOptions={name:"IncompatibleDescriptorOptions",title:"Cannot set both `densities` and `widths`",message:"Only one of `densities` or `widths` can be specified. In most cases, you'll probably want to use only `widths` if you require specific widths.",hint:"Those attributes are used to construct a `srcset` attribute, which cannot have both `x` and `w` descriptors."},NoImageMetadata={name:"NoImageMetadata",title:"Could not process image metadata.",message:e=>`Could not process image metadata${e?` for \`${e}\``:""}.`,hint:"This is often caused by a corrupted or malformed image. Re-exporting the image from your image editor may fix this issue."},ResponseSentError={name:"ResponseSentError",title:"Unable to set response.",message:"The response has already been sent to the browser and cannot be altered."},MiddlewareNoDataOrNextCalled={name:"MiddlewareNoDataOrNextCalled",title:"The middleware didn't return a `Response`.",message:"Make sure your middleware returns a `Response` object, either directly or by returning the `Response` from calling the `next` function."},MiddlewareNotAResponse={name:"MiddlewareNotAResponse",title:"The middleware returned something that is not a `Response` object.",message:"Any data returned from middleware must be a valid `Response` object."},EndpointDidNotReturnAResponse={name:"EndpointDidNotReturnAResponse",title:"The endpoint did not return a `Response`.",message:"An endpoint must return either a `Response`, or a `Promise` that resolves with a `Response`."},LocalsNotAnObject={name:"LocalsNotAnObject",title:"Value assigned to `locals` is not accepted.",message:"`locals` can only be assigned to an object. Other values like numbers, strings, etc. are not accepted.",hint:"If you tried to remove some information from the `locals` object, try to use `delete` or set the property to `undefined`."},LocalsReassigned={name:"LocalsReassigned",title:"`locals` must not be reassigned.",message:"`locals` can not be assigned directly.",hint:"Set a `locals` property instead."},AstroResponseHeadersReassigned={name:"AstroResponseHeadersReassigned",title:"`Astro.response.headers` must not be reassigned.",message:"Individual headers can be added to and removed from `Astro.response.headers`, but it must not be replaced with another instance of `Headers` altogether.",hint:"Consider using `Astro.response.headers.add()`, and `Astro.response.headers.delete()`."},LocalImageUsedWrongly={name:"LocalImageUsedWrongly",title:"Local images must be imported.",message:e=>`\`Image\`'s and \`getImage\`'s \`src\` parameter must be an imported image or an URL, it cannot be a string filepath. Received \`${e}\`.`,hint:"If you want to use an image from your `src` folder, you need to either import it or if the image is coming from a content collection, use the [image() schema helper](https://docs.astro.build/en/guides/images/#images-in-content-collections). See https://docs.astro.build/en/guides/images/#src-required for more information on the `src` property."},AstroGlobUsedOutside={name:"AstroGlobUsedOutside",title:"Astro.glob() used outside of an Astro file.",message:e=>`\`Astro.glob(${e})\` can only be used in \`.astro\` files. \`import.meta.glob(${e})\` can be used instead to achieve a similar result.`,hint:"See Vite's documentation on `import.meta.glob` for more information: https://vite.dev/guide/features.html#glob-import"},AstroGlobNoMatch={name:"AstroGlobNoMatch",title:"Astro.glob() did not match any files.",message:e=>`\`Astro.glob(${e})\` did not return any matching files.`,hint:"Check the pattern for typos."},i18nNoLocaleFoundInPath={name:"i18nNoLocaleFoundInPath",title:"The path doesn't contain any locale",message:"You tried to use an i18n utility on a path that doesn't contain any locale. You can use `pathHasLocale` first to determine if the path has a locale."},RewriteWithBodyUsed={name:"RewriteWithBodyUsed",title:"Cannot use Astro.rewrite after the request body has been read",message:"Astro.rewrite() cannot be used if the request body has already been read. If you need to read the body, first clone the request."},ForbiddenRewrite={name:"ForbiddenRewrite",title:"Forbidden rewrite to a static route.",message:(e,t,r)=>`You tried to rewrite the on-demand route '${e}' with the static route '${t}', when using the 'server' output. \n\nThe static route '${t}' is rendered by the component\n'${r}', which is marked as prerendered. This is a forbidden operation because during the build the component '${r}' is compiled to an\nHTML file, which can't be retrieved at runtime by Astro.`,hint:e=>`Add \`export const prerender = false\` to the component '${e}', or use a Astro.redirect().`},ExperimentalFontsNotEnabled={name:"ExperimentalFontsNotEnabled",title:"Experimental fonts are not enabled",message:"The Font component is used but experimental fonts have not been registered in the config.",hint:"Check that you have enabled experimental fonts and also configured your preferred fonts."},FontFamilyNotFound={name:"FontFamilyNotFound",title:"Font family not found",message:e=>`No data was found for the \`"${e}"\` family passed to the \`<Font>\` component.`,hint:"This is often caused by a typo. Check that your Font component is using a `cssVariable` specified in your config."},CspNotEnabled={name:"CspNotEnabled",title:"CSP feature isn't enabled",message:"The `experimental.csp` configuration isn't enabled."},ActionsReturnedInvalidDataError={name:"ActionsReturnedInvalidDataError",title:"Action handler returned invalid data.",message:e=>`Action handler returned invalid data. Handlers should return serializable data types like objects, arrays, strings, and numbers. Parse error: ${e}`,hint:"See the devalue library for all supported types: https://github.com/rich-harris/devalue"},ActionNotFoundError={name:"ActionNotFoundError",title:"Action not found.",message:e=>`The server received a request for an action named \`${e}\` but could not find a match. If you renamed an action, check that you've updated your \`actions/index\` file and your calling code to match.`,hint:"You can run `astro check` to detect type errors caused by mismatched action names."},SessionStorageInitError={name:"SessionStorageInitError",title:"Session storage could not be initialized.",message:(e,t)=>`Error when initializing session storage${t?` with driver \`${t}\``:""}. \`${e??""}\``,hint:"For more information, see https://docs.astro.build/en/guides/sessions/"},SessionStorageSaveError={name:"SessionStorageSaveError",title:"Session data could not be saved.",message:(e,t)=>`Error when saving session data${t?` with driver \`${t}\``:""}. \`${e??""}\``,hint:"For more information, see https://docs.astro.build/en/guides/sessions/"};function validateArgs(e){return 3===e.length&&!(!e[0]||"object"!=typeof e[0])}function baseCreateComponent(e,t,r){const n=t?.split("/").pop()?.replace(".astro","")??"",s=(...t)=>{if(!validateArgs(t))throw new AstroError({...InvalidComponentArgs,message:InvalidComponentArgs.message(n)});return e(...t)};return Object.defineProperty(s,"name",{value:n,writable:!1}),s.isAstroComponentFactory=!0,s.moduleId=t,s.propagation=r,s}function createComponentWithOptions(e){return baseCreateComponent(e.factory,e.moduleId,e.propagation)}function createComponent(e,t,r){return"function"==typeof e?baseCreateComponent(e,t,r):createComponentWithOptions(e)}function createAstroGlobFn(){return e=>{if(console.warn("Astro.glob is deprecated and will be removed in a future major version of Astro.\nUse import.meta.glob instead: https://vitejs.dev/guide/features.html#glob-import"),"string"==typeof e)throw new AstroError({...AstroGlobUsedOutside,message:AstroGlobUsedOutside.message(JSON.stringify(e))});let t=[...Object.values(e)];if(0===t.length)throw new AstroError({...AstroGlobNoMatch,message:AstroGlobNoMatch.message(JSON.stringify(e))});return Promise.all(t.map((e=>e())))}}function createAstro(e){return{site:new URL(e),generator:"Astro v5.12.4",glob:createAstroGlobFn()}}let FORCE_COLOR,NODE_DISABLE_COLORS,NO_COLOR,TERM,isTTY=!0;"undefined"!=typeof process&&(({FORCE_COLOR:FORCE_COLOR,NODE_DISABLE_COLORS:NODE_DISABLE_COLORS,NO_COLOR:NO_COLOR,TERM:TERM}=process.env||{}),isTTY=process.stdout&&process.stdout.isTTY);const $={enabled:!NODE_DISABLE_COLORS&&null==NO_COLOR&&"dumb"!==TERM&&(null!=FORCE_COLOR&&"0"!==FORCE_COLOR||isTTY)};function init(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`[${e}m`,s=`[${t}m`;return function(e){return $.enabled&&null!=e?n+(~(""+e).indexOf(s)?e.replace(r,s+n):e)+s:e}}const bold=init(1,22),dim=init(2,22),red=init(31,39),green=init(32,39),yellow=init(33,39),blue=init(34,39);async function renderEndpoint(e,t,r,n){const{request:s,url:a}=t,o=s.method.toUpperCase();let i=e[o]??e.ALL;if(!i&&"HEAD"===o&&e.GET&&(i=e.GET),r&&!["GET","HEAD"].includes(o)&&n.warn("router",`${a.pathname} ${bold(o)} requests are not available in static endpoints. Mark this page as server-rendered (\`export const prerender = false;\`) or update your config to \`output: 'server'\` to make all your pages server-rendered by default.`),void 0===i)return n.warn("router",`No API Route handler exists for the method "${o}" for the route "${a.pathname}".\nFound handlers: ${Object.keys(e).map((e=>JSON.stringify(e))).join(", ")}\n`+("all"in e?"One of the exported handlers is \"all\" (lowercase), did you mean to export 'ALL'?\n":"")),new Response(null,{status:404});if("function"!=typeof i)return n.error("router",`The route "${a.pathname}" exports a value for the method "${o}", but it is of the type ${typeof i} instead of a function.`),new Response(null,{status:500});let d=await i.call(e,t);if(!d||d instanceof Response==!1)throw new AstroError(EndpointDidNotReturnAResponse);if(REROUTABLE_STATUS_CODES.includes(d.status))try{d.headers.set("X-Astro-Reroute","no")}catch(e){if(!e.message?.includes("immutable"))throw e;d=new Response(d.body,d),d.headers.set("X-Astro-Reroute","no")}return"HEAD"===o?new Response(null,d):d}const{replace:replace}="",ca=/[&<>'"]/g,esca={"&":"&amp;","<":"&lt;",">":"&gt;","'":"&#39;",'"':"&quot;"},pe=e=>esca[e],escape=e=>replace.call(e,ca,pe);function isPromise(e){return!!e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}async function*streamAsyncIterator(e){const t=e.getReader();try{for(;;){const{done:e,value:r}=await t.read();if(e)return;yield r}}finally{t.releaseLock()}}const escapeHTML=escape;class HTMLBytes extends Uint8Array{}Object.defineProperty(HTMLBytes.prototype,Symbol.toStringTag,{get:()=>"HTMLBytes"});class HTMLString extends String{get[Symbol.toStringTag](){return"HTMLString"}}const markHTMLString=e=>e instanceof HTMLString?e:"string"==typeof e?new HTMLString(e):e;function isHTMLString(e){return"[object HTMLString]"===Object.prototype.toString.call(e)}function markHTMLBytes(e){return new HTMLBytes(e)}function hasGetReader(e){return"function"==typeof e.getReader}async function*unescapeChunksAsync(e){if(hasGetReader(e))for await(const t of streamAsyncIterator(e))yield unescapeHTML(t);else for await(const t of e)yield unescapeHTML(t)}function*unescapeChunks(e){for(const t of e)yield unescapeHTML(t)}function unescapeHTML(e){if(e&&"object"==typeof e){if(e instanceof Uint8Array)return markHTMLBytes(e);if(e instanceof Response&&e.body){return unescapeChunksAsync(e.body)}if("function"==typeof e.then)return Promise.resolve(e).then((e=>unescapeHTML(e)));if(e[Symbol.for("astro:slot-string")])return e;if(Symbol.iterator in e)return unescapeChunks(e);if(Symbol.asyncIterator in e||hasGetReader(e))return unescapeChunksAsync(e)}return markHTMLString(e)}const AstroJSX="astro:jsx";function isVNode(e){return e&&"object"==typeof e&&e[AstroJSX]}function isAstroComponentFactory(e){return null!=e&&!0===e.isAstroComponentFactory}function isAPropagatingComponent(e,t){const r=getPropagationHint(e,t);return"in-tree"===r||"self"===r}function getPropagationHint(e,t){let r=t.propagation||"none";return t.moduleId&&e.componentMetadata.has(t.moduleId)&&"none"===r&&(r=e.componentMetadata.get(t.moduleId).propagation),r}function r(e){var t,n,s="";if("string"==typeof e||"number"==typeof e)s+=e;else if("object"==typeof e)if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(n=r(e[t]))&&(s&&(s+=" "),s+=n)}else for(n in e)e[n]&&(s&&(s+=" "),s+=n);return s}function clsx(){for(var e,t,n=0,s="",a=arguments.length;n<a;n++)(e=arguments[n])&&(t=r(e))&&(s&&(s+=" "),s+=t);return s}const PROP_TYPE={Value:0,JSON:1,RegExp:2,Date:3,Map:4,Set:5,BigInt:6,URL:7,Uint8Array:8,Uint16Array:9,Uint32Array:10,Infinity:11};function serializeArray(e,t={},r=new WeakSet){if(r.has(e))throw new Error(`Cyclic reference detected while serializing props for <${t.displayName} client:${t.hydrate}>!\n\nCyclic references cannot be safely serialized for client-side usage. Please remove the cyclic reference.`);r.add(e);const n=e.map((e=>convertToSerializedForm(e,t,r)));return r.delete(e),n}function serializeObject(e,t={},r=new WeakSet){if(r.has(e))throw new Error(`Cyclic reference detected while serializing props for <${t.displayName} client:${t.hydrate}>!\n\nCyclic references cannot be safely serialized for client-side usage. Please remove the cyclic reference.`);r.add(e);const n=Object.fromEntries(Object.entries(e).map((([e,n])=>[e,convertToSerializedForm(n,t,r)])));return r.delete(e),n}function convertToSerializedForm(e,t={},r=new WeakSet){switch(Object.prototype.toString.call(e)){case"[object Date]":return[PROP_TYPE.Date,e.toISOString()];case"[object RegExp]":return[PROP_TYPE.RegExp,e.source];case"[object Map]":return[PROP_TYPE.Map,serializeArray(Array.from(e),t,r)];case"[object Set]":return[PROP_TYPE.Set,serializeArray(Array.from(e),t,r)];case"[object BigInt]":return[PROP_TYPE.BigInt,e.toString()];case"[object URL]":return[PROP_TYPE.URL,e.toString()];case"[object Array]":return[PROP_TYPE.JSON,serializeArray(e,t,r)];case"[object Uint8Array]":return[PROP_TYPE.Uint8Array,Array.from(e)];case"[object Uint16Array]":return[PROP_TYPE.Uint16Array,Array.from(e)];case"[object Uint32Array]":return[PROP_TYPE.Uint32Array,Array.from(e)];default:return null!==e&&"object"==typeof e?[PROP_TYPE.Value,serializeObject(e,t,r)]:e===1/0?[PROP_TYPE.Infinity,1]:e===-1/0?[PROP_TYPE.Infinity,-1]:void 0===e?[PROP_TYPE.Value]:[PROP_TYPE.Value,e]}}function serializeProps(e,t){return JSON.stringify(serializeObject(e,t))}const transitionDirectivesToCopyOnIsland=Object.freeze(["data-astro-transition-scope","data-astro-transition-persist","data-astro-transition-persist-props"]);function extractDirectives(e,t){let r={isPage:!1,hydration:null,props:{},propsWithoutTransitionAttributes:{}};for(const[n,s]of Object.entries(e))if(n.startsWith("server:")&&"server:root"===n&&(r.isPage=!0),n.startsWith("client:"))switch(r.hydration||(r.hydration={directive:"",value:"",componentUrl:"",componentExport:{value:""}}),n){case"client:component-path":r.hydration.componentUrl=s;break;case"client:component-export":r.hydration.componentExport.value=s;break;case"client:component-hydration":case"client:display-name":break;default:if(r.hydration.directive=n.split(":")[1],r.hydration.value=s,!t.has(r.hydration.directive)){const e=Array.from(t.keys()).map((e=>`client:${e}`)).join(", ");throw new Error(`Error: invalid hydration directive "${n}". Supported hydration methods: ${e}`)}if("media"===r.hydration.directive&&"string"!=typeof r.hydration.value)throw new AstroError(MissingMediaQueryDirective)}else r.props[n]=s,transitionDirectivesToCopyOnIsland.includes(n)||(r.propsWithoutTransitionAttributes[n]=s);for(const t of Object.getOwnPropertySymbols(e))r.props[t]=e[t],r.propsWithoutTransitionAttributes[t]=e[t];return r}async function generateHydrateScript(e,t){const{renderer:r,result:n,astroId:s,props:a,attrs:o}=e,{hydrate:i,componentUrl:d,componentExport:c}=t;if(!c.value)throw new AstroError({...NoMatchingImport,message:NoMatchingImport.message(t.displayName)});const l={children:"",props:{uid:s}};if(o)for(const[e,t]of Object.entries(o))l.props[e]=escapeHTML(t);l.props["component-url"]=await n.resolve(decodeURI(d)),r.clientEntrypoint&&(l.props["component-export"]=c.value,l.props["renderer-url"]=await n.resolve(decodeURI(r.clientEntrypoint.toString())),l.props.props=escapeHTML(serializeProps(a,t))),l.props.ssr="",l.props.client=i;let u=await n.resolve("astro:scripts/before-hydration.js");return u.length&&(l.props["before-hydration-url"]=u),l.props.opts=escapeHTML(JSON.stringify({name:t.displayName,value:t.hydrateArgs||""})),transitionDirectivesToCopyOnIsland.forEach((e=>{void 0!==a[e]&&(l.props[e]=a[e])})),l}const dictionary="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXY",binary=61;function bitwise(e){let t=0;if(0===e.length)return t;for(let r=0;r<e.length;r++){t=(t<<5)-t+e.charCodeAt(r),t&=t}return t}function shorthash(e){let t,r="",n=bitwise(e);const s=n<0?"Z":"";for(n=Math.abs(n);n>=61;)t=n%61,n=Math.floor(n/61),r=dictionary[t]+r;return n>0&&(r=dictionary[n]+r),s+r}const headAndContentSym=Symbol.for("astro.headAndContent");function isHeadAndContent(e){return"object"==typeof e&&null!==e&&!!e[headAndContentSym]}function createThinHead(){return{[headAndContentSym]:!0}}var astro_island_prebuilt_default='(()=>{var A=Object.defineProperty;var g=(i,o,a)=>o in i?A(i,o,{enumerable:!0,configurable:!0,writable:!0,value:a}):i[o]=a;var d=(i,o,a)=>g(i,typeof o!="symbol"?o+"":o,a);{let i={0:t=>m(t),1:t=>a(t),2:t=>new RegExp(t),3:t=>new Date(t),4:t=>new Map(a(t)),5:t=>new Set(a(t)),6:t=>BigInt(t),7:t=>new URL(t),8:t=>new Uint8Array(t),9:t=>new Uint16Array(t),10:t=>new Uint32Array(t),11:t=>1/0*t},o=t=>{let[l,e]=t;return l in i?i[l](e):void 0},a=t=>t.map(o),m=t=>typeof t!="object"||t===null?t:Object.fromEntries(Object.entries(t).map(([l,e])=>[l,o(e)]));class y extends HTMLElement{constructor(){super(...arguments);d(this,"Component");d(this,"hydrator");d(this,"hydrate",async()=>{var b;if(!this.hydrator||!this.isConnected)return;let e=(b=this.parentElement)==null?void 0:b.closest("astro-island[ssr]");if(e){e.addEventListener("astro:hydrate",this.hydrate,{once:!0});return}let c=this.querySelectorAll("astro-slot"),n={},h=this.querySelectorAll("template[data-astro-template]");for(let r of h){let s=r.closest(this.tagName);s!=null&&s.isSameNode(this)&&(n[r.getAttribute("data-astro-template")||"default"]=r.innerHTML,r.remove())}for(let r of c){let s=r.closest(this.tagName);s!=null&&s.isSameNode(this)&&(n[r.getAttribute("name")||"default"]=r.innerHTML)}let p;try{p=this.hasAttribute("props")?m(JSON.parse(this.getAttribute("props"))):{}}catch(r){let s=this.getAttribute("component-url")||"<unknown>",v=this.getAttribute("component-export");throw v&&(s+=` (export ${v})`),console.error(`[hydrate] Error parsing props for component ${s}`,this.getAttribute("props"),r),r}let u;await this.hydrator(this)(this.Component,p,n,{client:this.getAttribute("client")}),this.removeAttribute("ssr"),this.dispatchEvent(new CustomEvent("astro:hydrate"))});d(this,"unmount",()=>{this.isConnected||this.dispatchEvent(new CustomEvent("astro:unmount"))})}disconnectedCallback(){document.removeEventListener("astro:after-swap",this.unmount),document.addEventListener("astro:after-swap",this.unmount,{once:!0})}connectedCallback(){if(!this.hasAttribute("await-children")||document.readyState==="interactive"||document.readyState==="complete")this.childrenConnectedCallback();else{let e=()=>{document.removeEventListener("DOMContentLoaded",e),c.disconnect(),this.childrenConnectedCallback()},c=new MutationObserver(()=>{var n;((n=this.lastChild)==null?void 0:n.nodeType)===Node.COMMENT_NODE&&this.lastChild.nodeValue==="astro:end"&&(this.lastChild.remove(),e())});c.observe(this,{childList:!0}),document.addEventListener("DOMContentLoaded",e)}}async childrenConnectedCallback(){let e=this.getAttribute("before-hydration-url");e&&await import(e),this.start()}async start(){let e=JSON.parse(this.getAttribute("opts")),c=this.getAttribute("client");if(Astro[c]===void 0){window.addEventListener(`astro:${c}`,()=>this.start(),{once:!0});return}try{await Astro[c](async()=>{let n=this.getAttribute("renderer-url"),[h,{default:p}]=await Promise.all([import(this.getAttribute("component-url")),n?import(n):()=>()=>{}]),u=this.getAttribute("component-export")||"default";if(!u.includes("."))this.Component=h[u];else{this.Component=h;for(let f of u.split("."))this.Component=this.Component[f]}return this.hydrator=p,this.hydrate},e,this)}catch(n){console.error(`[astro-island] Error hydrating ${this.getAttribute("component-url")}`,n)}}attributeChangedCallback(){this.hydrate()}}d(y,"observedAttributes",["props"]),customElements.get("astro-island")||customElements.define("astro-island",y)}})();';const ISLAND_STYLES="astro-island,astro-slot,astro-static-slot{display:contents}";function determineIfNeedsHydrationScript(e){return!e._metadata.hasHydrationScript&&(e._metadata.hasHydrationScript=!0)}function determinesIfNeedsDirectiveScript(e,t){return!e._metadata.hasDirectives.has(t)&&(e._metadata.hasDirectives.add(t),!0)}function getDirectiveScriptText(e,t){const r=e.clientDirectives.get(t);if(!r)throw new Error(`Unknown directive: ${t}`);return r}function getPrescripts(e,t,r){switch(t){case"both":return`<style>${ISLAND_STYLES}</style><script>${getDirectiveScriptText(e,r)}<\/script><script>${astro_island_prebuilt_default}<\/script>`;case"directive":return`<script>${getDirectiveScriptText(e,r)}<\/script>`}}function renderCspContent(e){const t=new Set,r=new Set;for(const r of e.scriptHashes)t.add(`'${r}'`);for(const t of e.styleHashes)r.add(`'${t}'`);for(const t of e._metadata.extraStyleHashes)r.add(`'${t}'`);for(const r of e._metadata.extraScriptHashes)t.add(`'${r}'`);let n="";e.directives.length>0&&(n=e.directives.join(";")+";");let s="'self'";e.scriptResources.length>0&&(s=e.scriptResources.map((e=>`${e}`)).join(" "));let a="'self'";e.styleResources.length>0&&(a=e.styleResources.map((e=>`${e}`)).join(" "));const o=e.isStrictDynamic?" 'strict-dynamic'":"";return`${n} ${`script-src ${s} ${Array.from(t).join(" ")}${o};`} ${`style-src ${a} ${Array.from(r).join(" ")};`}`}const RenderInstructionSymbol=Symbol.for("astro:render");function createRenderInstruction(e){return Object.defineProperty(e,RenderInstructionSymbol,{value:!0})}function isRenderInstruction(e){return e&&"object"==typeof e&&e[RenderInstructionSymbol]}const voidElementNames=/^(area|base|br|col|command|embed|hr|img|input|keygen|link|meta|param|source|track|wbr)$/i,htmlBooleanAttributes=/^(?:allowfullscreen|async|autofocus|autoplay|checked|controls|default|defer|disabled|disablepictureinpicture|disableremoteplayback|formnovalidate|hidden|inert|loop|nomodule|novalidate|open|playsinline|readonly|required|reversed|scoped|seamless|selected|itemscope)$/i,AMPERSAND_REGEX=/&/g,DOUBLE_QUOTE_REGEX=/"/g,STATIC_DIRECTIVES=new Set(["set:html","set:text"]),toIdent=e=>e.trim().replace(/(?!^)\b\w|\s+|\W+/g,((e,t)=>/\W/.test(e)?"":0===t?e:e.toUpperCase())),toAttributeString=(e,t=!0)=>t?String(e).replace(AMPERSAND_REGEX,"&#38;").replace(DOUBLE_QUOTE_REGEX,"&#34;"):e,kebab=e=>e.toLowerCase()===e?e:e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)),toStyleString=e=>Object.entries(e).filter((([e,t])=>"string"==typeof t&&t.trim()||"number"==typeof t)).map((([e,t])=>"-"!==e[0]&&"-"!==e[1]?`${kebab(e)}:${t}`:`${e}:${t}`)).join(";");function defineScriptVars(e){let t="";for(const[r,n]of Object.entries(e))t+=`const ${toIdent(r)} = ${JSON.stringify(n)?.replace(/<\/script>/g,"\\x3C/script>")};\n`;return markHTMLString(t)}function formatList(e){return 1===e.length?e[0]:`${e.slice(0,-1).join(", ")} or ${e[e.length-1]}`}function isCustomElement(e){return e.includes("-")}function handleBooleanAttribute(e,t,r,n){return n&&isCustomElement(n)?markHTMLString(` ${e}="${toAttributeString(t,r)}"`):markHTMLString(t?` ${e}`:"")}function addAttribute(e,t,r=!0,n=""){if(null==e)return"";if(STATIC_DIRECTIVES.has(t))return console.warn(`[astro] The "${t}" directive cannot be applied dynamically at runtime. It will not be rendered as an attribute.\n\nMake sure to use the static attribute syntax (\`${t}={value}\`) instead of the dynamic spread syntax (\`{...{ "${t}": value }}\`).`),"";if("class:list"===t){const n=toAttributeString(clsx(e),r);return""===n?"":markHTMLString(` ${t.slice(0,-5)}="${n}"`)}if("style"===t&&!(e instanceof HTMLString)){if(Array.isArray(e)&&2===e.length)return markHTMLString(` ${t}="${toAttributeString(`${toStyleString(e[0])};${e[1]}`,r)}"`);if("object"==typeof e)return markHTMLString(` ${t}="${toAttributeString(toStyleString(e),r)}"`)}return"className"===t?markHTMLString(` class="${toAttributeString(e,r)}"`):"string"==typeof e&&e.includes("&")&&isHttpUrl(e)?markHTMLString(` ${t}="${toAttributeString(e,!1)}"`):htmlBooleanAttributes.test(t)?handleBooleanAttribute(t,e,r,n):""===e?markHTMLString(` ${t}`):"popover"===t&&"boolean"==typeof e||"download"===t&&"boolean"==typeof e?handleBooleanAttribute(t,e,r,n):markHTMLString(` ${t}="${toAttributeString(e,r)}"`)}function internalSpreadAttributes(e,t=!0,r){let n="";for(const[s,a]of Object.entries(e))n+=addAttribute(a,s,t,r);return markHTMLString(n)}function renderElement$1(e,{props:t,children:r=""},n=!0){const{lang:s,"data-astro-id":a,"define:vars":o,...i}=t;return o&&("style"===e&&(delete i["is:global"],delete i["is:scoped"]),"script"===e&&(delete i.hoist,r=defineScriptVars(o)+"\n"+r)),null!=r&&""!=r||!voidElementNames.test(e)?`<${e}${internalSpreadAttributes(i,n,e)}>${r}</${e}>`:`<${e}${internalSpreadAttributes(i,n,e)}>`}const noop=()=>{};class BufferedRenderer{chunks=[];renderPromise;destination;flushed=!1;constructor(e,t){this.destination=e,this.renderPromise=t(this),isPromise(this.renderPromise)&&Promise.resolve(this.renderPromise).catch(noop)}write(e){this.flushed?this.destination.write(e):this.chunks.push(e)}flush(){if(this.flushed)throw new Error("The render buffer has already been flushed.");this.flushed=!0;for(const e of this.chunks)this.destination.write(e);return this.renderPromise}}function createBufferedRenderer(e,t){return new BufferedRenderer(e,t)}const isNode="undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process),isDeno="undefined"!=typeof Deno;function promiseWithResolvers(){let e,t;return{promise:new Promise(((r,n)=>{e=r,t=n})),resolve:e,reject:t}}const VALID_PROTOCOLS=["http:","https:"];function isHttpUrl(e){try{const t=new URL(e);return VALID_PROTOCOLS.includes(t.protocol)}catch{return!1}}const uniqueElements=(e,t,r)=>{const n=JSON.stringify(e.props),s=e.children;return t===r.findIndex((e=>JSON.stringify(e.props)===n&&e.children==s))};function renderAllHeadContent(e){e._metadata.hasRenderedHead=!0;let t="";e.shouldInjectCspMetaTags&&"meta"===e.cspDestination&&(t+=renderElement$1("meta",{props:{"http-equiv":"content-security-policy",content:renderCspContent(e)},children:""},!1));const r=Array.from(e.styles).filter(uniqueElements).map((e=>"stylesheet"===e.props.rel?renderElement$1("link",e):renderElement$1("style",e)));e.styles.clear();const n=Array.from(e.scripts).filter(uniqueElements).map((t=>(e.userAssetsBase&&(t.props.src=("/"===e.base?"":e.base)+e.userAssetsBase+t.props.src),renderElement$1("script",t,!1)))),s=Array.from(e.links).filter(uniqueElements).map((e=>renderElement$1("link",e,!1)));if(t+=r.join("\n")+s.join("\n")+n.join("\n"),e._metadata.extraHead.length>0)for(const r of e._metadata.extraHead)t+=r;return markHTMLString(t)}function renderHead(){return createRenderInstruction({type:"head"})}function maybeRenderHead(){return createRenderInstruction({type:"maybe-head"})}function encodeHexUpperCase(e){let t="";for(let r=0;r<e.length;r++)t+=alphabetUpperCase[e[r]>>4],t+=alphabetUpperCase[15&e[r]];return t}function decodeHex(e){if(e.length%2!=0)throw new Error("Invalid hex string");const t=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2){if(!(e[r]in decodeMap))throw new Error("Invalid character");if(!(e[r+1]in decodeMap))throw new Error("Invalid character");t[r/2]|=decodeMap[e[r]]<<4,t[r/2]|=decodeMap[e[r+1]]}return t}const alphabetUpperCase="0123456789ABCDEF",decodeMap={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};var EncodingPadding$1,DecodingPadding$1;function encodeBase64(e){return encodeBase64_internal(e,base64Alphabet,EncodingPadding.Include)}function encodeBase64_internal(e,t,r){let n="";for(let s=0;s<e.byteLength;s+=3){let a=0,o=0;for(let t=0;t<3&&s+t<e.byteLength;t++)a=a<<8|e[s+t],o+=8;for(let e=0;e<4;e++)o>=6?(n+=t[a>>o-6&63],o-=6):o>0?(n+=t[a<<6-o&63],o=0):r===EncodingPadding.Include&&(n+="=")}return n}!function(e){e[e.Include=0]="Include",e[e.None=1]="None"}(EncodingPadding$1||(EncodingPadding$1={})),function(e){e[e.Required=0]="Required",e[e.Ignore=1]="Ignore"}(DecodingPadding$1||(DecodingPadding$1={}));const base64Alphabet="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function decodeBase64(e){return decodeBase64_internal(e,base64DecodeMap,DecodingPadding.Required)}function decodeBase64_internal(e,t,r){const n=new Uint8Array(3*Math.ceil(e.length/4));let s=0;for(let a=0;a<e.length;a+=4){let o=0,i=0;for(let n=0;n<4;n++)if(!(r===DecodingPadding.Required&&"="===e[a+n]||r===DecodingPadding.Ignore&&(a+n>=e.length||"="===e[a+n]))){if(n>0&&"="===e[a+n-1])throw new Error("Invalid padding");if(!(e[a+n]in t))throw new Error("Invalid character");o|=t[e[a+n]]<<6*(3-n),i+=6}if(i<24){let e;if(12===i)e=65535&o;else{if(18!==i)throw new Error("Invalid padding");e=255&o}if(0!==e)throw new Error("Invalid padding")}const d=Math.floor(i/8);for(let e=0;e<d;e++)n[s]=o>>16-8*e&255,s++}return n.slice(0,s)}var EncodingPadding,DecodingPadding;!function(e){e[e.Include=0]="Include",e[e.None=1]="None"}(EncodingPadding||(EncodingPadding={})),function(e){e[e.Required=0]="Required",e[e.Ignore=1]="Ignore"}(DecodingPadding||(DecodingPadding={}));const base64DecodeMap={0:52,1:53,2:54,3:55,4:56,5:57,6:58,7:59,8:60,9:61,A:0,B:1,C:2,D:3,E:4,F:5,G:6,H:7,I:8,J:9,K:10,L:11,M:12,N:13,O:14,P:15,Q:16,R:17,S:18,T:19,U:20,V:21,W:22,X:23,Y:24,Z:25,a:26,b:27,c:28,d:29,e:30,f:31,g:32,h:33,i:34,j:35,k:36,l:37,m:38,n:39,o:40,p:41,q:42,r:43,s:44,t:45,u:46,v:47,w:48,x:49,y:50,z:51,"+":62,"/":63};var util,objectUtil;!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw new Error},e.arrayToEnum=e=>{const t={};for(const r of e)t[r]=r;return t},e.getValidEnumValues=t=>{const r=e.objectKeys(t).filter((e=>"number"!=typeof t[t[e]])),n={};for(const e of r)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map((function(e){return t[e]})),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(const r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map((e=>"string"==typeof e?`'${e}'`:e)).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(util||(util={})),function(e){e.mergeShapes=(e,t)=>({...e,...t})}(objectUtil||(objectUtil={}));const ZodParsedType=util.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),getParsedType=e=>{switch(typeof e){case"undefined":return ZodParsedType.undefined;case"string":return ZodParsedType.string;case"number":return Number.isNaN(e)?ZodParsedType.nan:ZodParsedType.number;case"boolean":return ZodParsedType.boolean;case"function":return ZodParsedType.function;case"bigint":return ZodParsedType.bigint;case"symbol":return ZodParsedType.symbol;case"object":return Array.isArray(e)?ZodParsedType.array:null===e?ZodParsedType.null:e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch?ZodParsedType.promise:"undefined"!=typeof Map&&e instanceof Map?ZodParsedType.map:"undefined"!=typeof Set&&e instanceof Set?ZodParsedType.set:"undefined"!=typeof Date&&e instanceof Date?ZodParsedType.date:ZodParsedType.object;default:return ZodParsedType.unknown}},ZodIssueCode=util.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class ZodError extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(e){return e.message},r={_errors:[]},n=e=>{for(const s of e.issues)if("invalid_union"===s.code)s.unionErrors.map(n);else if("invalid_return_type"===s.code)n(s.returnTypeError);else if("invalid_arguments"===s.code)n(s.argumentsError);else if(0===s.path.length)r._errors.push(t(s));else{let e=r,n=0;for(;n<s.path.length;){const r=s.path[n];n===s.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(s))):e[r]=e[r]||{_errors:[]},e=e[r],n++}}};return n(this),r}static assert(e){if(!(e instanceof ZodError))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,util.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){const t={},r=[];for(const n of this.issues)if(n.path.length>0){const r=n.path[0];t[r]=t[r]||[],t[r].push(e(n))}else r.push(e(n));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}ZodError.create=e=>new ZodError(e);const errorMap=(e,t)=>{let r;switch(e.code){case ZodIssueCode.invalid_type:r=e.received===ZodParsedType.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case ZodIssueCode.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,util.jsonStringifyReplacer)}`;break;case ZodIssueCode.unrecognized_keys:r=`Unrecognized key(s) in object: ${util.joinValues(e.keys,", ")}`;break;case ZodIssueCode.invalid_union:r="Invalid input";break;case ZodIssueCode.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${util.joinValues(e.options)}`;break;case ZodIssueCode.invalid_enum_value:r=`Invalid enum value. Expected ${util.joinValues(e.options)}, received '${e.received}'`;break;case ZodIssueCode.invalid_arguments:r="Invalid function arguments";break;case ZodIssueCode.invalid_return_type:r="Invalid function return type";break;case ZodIssueCode.invalid_date:r="Invalid date";break;case ZodIssueCode.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:util.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case ZodIssueCode.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type||"bigint"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case ZodIssueCode.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case ZodIssueCode.custom:r="Invalid input";break;case ZodIssueCode.invalid_intersection_types:r="Intersection results could not be merged";break;case ZodIssueCode.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case ZodIssueCode.not_finite:r="Number must be finite";break;default:r=t.defaultError,util.assertNever(e)}return{message:r}};let overrideErrorMap=errorMap;function getErrorMap(){return overrideErrorMap}const makeIssue=e=>{const{data:t,path:r,errorMaps:n,issueData:s}=e,a=[...r,...s.path||[]],o={...s,path:a};if(void 0!==s.message)return{...s,path:a,message:s.message};let i="";const d=n.filter((e=>!!e)).slice().reverse();for(const e of d)i=e(o,{data:t,defaultError:i}).message;return{...s,path:a,message:i}};function addIssueToContext(e,t){const r=getErrorMap(),n=makeIssue({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,r,r===errorMap?void 0:errorMap].filter((e=>!!e))});e.common.issues.push(n)}class ParseStatus{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){const r=[];for(const n of t){if("aborted"===n.status)return INVALID;"dirty"===n.status&&e.dirty(),r.push(n.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){const r=[];for(const e of t){const t=await e.key,n=await e.value;r.push({key:t,value:n})}return ParseStatus.mergeObjectSync(e,r)}static mergeObjectSync(e,t){const r={};for(const n of t){const{key:t,value:s}=n;if("aborted"===t.status)return INVALID;if("aborted"===s.status)return INVALID;"dirty"===t.status&&e.dirty(),"dirty"===s.status&&e.dirty(),"__proto__"===t.value||void 0===s.value&&!n.alwaysSet||(r[t.value]=s.value)}return{status:e.value,value:r}}}const INVALID=Object.freeze({status:"aborted"}),DIRTY=e=>({status:"dirty",value:e}),OK=e=>({status:"valid",value:e}),isAborted=e=>"aborted"===e.status,isDirty=e=>"dirty"===e.status,isValid=e=>"valid"===e.status,isAsync=e=>"undefined"!=typeof Promise&&e instanceof Promise;var errorUtil;!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(errorUtil||(errorUtil={}));class ParseInputLazyPath{constructor(e,t,r,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=n}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const handleResult=(e,t)=>{if(isValid(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new ZodError(e.common.issues);return this._error=t,this._error}}};function processCreateParams(e){if(!e)return{};const{errorMap:t,invalid_type_error:r,required_error:n,description:s}=e;if(t&&(r||n))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:s};return{errorMap:(t,s)=>{const{message:a}=e;return"invalid_enum_value"===t.code?{message:a??s.defaultError}:void 0===s.data?{message:a??n??s.defaultError}:"invalid_type"!==t.code?{message:s.defaultError}:{message:a??r??s.defaultError}},description:s}}class ZodType{get description(){return this._def.description}_getType(e){return getParsedType(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:getParsedType(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new ParseStatus,ctx:{common:e.parent.common,data:e.data,parsedType:getParsedType(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(isAsync(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){const r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:getParsedType(e)},n=this._parseSync({data:e,path:r.path,parent:r});return handleResult(r,n)}"~validate"(e){const t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:getParsedType(e)};if(!this["~standard"].async)try{const r=this._parseSync({data:e,path:[],parent:t});return isValid(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then((e=>isValid(e)?{value:e.value}:{issues:t.common.issues}))}async parseAsync(e,t){const r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){const r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:getParsedType(e)},n=this._parse({data:e,path:r.path,parent:r}),s=await(isAsync(n)?n:Promise.resolve(n));return handleResult(r,s)}refine(e,t){const r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement(((t,n)=>{const s=e(t),a=()=>n.addIssue({code:ZodIssueCode.custom,...r(t)});return"undefined"!=typeof Promise&&s instanceof Promise?s.then((e=>!!e||(a(),!1))):!!s||(a(),!1)}))}refinement(e,t){return this._refinement(((r,n)=>!!e(r)||(n.addIssue("function"==typeof t?t(r,n):t),!1)))}_refinement(e){return new ZodEffects({schema:this,typeName:ZodFirstPartyTypeKind.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ZodOptional.create(this,this._def)}nullable(){return ZodNullable.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ZodArray.create(this)}promise(){return ZodPromise.create(this,this._def)}or(e){return ZodUnion.create([this,e],this._def)}and(e){return ZodIntersection.create(this,e,this._def)}transform(e){return new ZodEffects({...processCreateParams(this._def),schema:this,typeName:ZodFirstPartyTypeKind.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new ZodDefault({...processCreateParams(this._def),innerType:this,defaultValue:t,typeName:ZodFirstPartyTypeKind.ZodDefault})}brand(){return new ZodBranded({typeName:ZodFirstPartyTypeKind.ZodBranded,type:this,...processCreateParams(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new ZodCatch({...processCreateParams(this._def),innerType:this,catchValue:t,typeName:ZodFirstPartyTypeKind.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return ZodPipeline.create(this,e)}readonly(){return ZodReadonly.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const cuidRegex=/^c[^\s-]{8,}$/i,cuid2Regex=/^[0-9a-z]+$/,ulidRegex=/^[0-9A-HJKMNP-TV-Z]{26}$/i,uuidRegex=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,nanoidRegex=/^[a-z0-9_-]{21}$/i,jwtRegex=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,durationRegex=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,emailRegex=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,_emojiRegex="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let emojiRegex;const ipv4Regex=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ipv4CidrRegex=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6Regex=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,ipv6CidrRegex=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64Regex=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64urlRegex=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,dateRegexSource="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",dateRegex=new RegExp(`^${dateRegexSource}$`);function timeRegexSource(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${e.precision?"+":"?"}`}function timeRegex(e){return new RegExp(`^${timeRegexSource(e)}$`)}function datetimeRegex(e){let t=`${dateRegexSource}T${timeRegexSource(e)}`;const r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,new RegExp(`^${t}$`)}function isValidIP(e,t){return!("v4"!==t&&t||!ipv4Regex.test(e))||!("v6"!==t&&t||!ipv6Regex.test(e))}function isValidJWT(e,t){if(!jwtRegex.test(e))return!1;try{const[r]=e.split(".");if(!r)return!1;const n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),s=JSON.parse(atob(n));return"object"==typeof s&&null!==s&&((!("typ"in s)||"JWT"===s?.typ)&&(!!s.alg&&(!t||s.alg===t)))}catch{return!1}}function isValidCidr(e,t){return!("v4"!==t&&t||!ipv4CidrRegex.test(e))||!("v6"!==t&&t||!ipv6CidrRegex.test(e))}class ZodString extends ZodType{_parse(e){this._def.coerce&&(e.data=String(e.data));if(this._getType(e)!==ZodParsedType.string){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.string,received:t.parsedType}),INVALID}const t=new ParseStatus;let r;for(const n of this._def.checks)if("min"===n.kind)e.data.length<n.value&&(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:ZodIssueCode.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),t.dirty());else if("max"===n.kind)e.data.length>n.value&&(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:ZodIssueCode.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),t.dirty());else if("length"===n.kind){const s=e.data.length>n.value,a=e.data.length<n.value;(s||a)&&(r=this._getOrReturnCtx(e,r),s?addIssueToContext(r,{code:ZodIssueCode.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}):a&&addIssueToContext(r,{code:ZodIssueCode.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}),t.dirty())}else if("email"===n.kind)emailRegex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"email",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty());else if("emoji"===n.kind)emojiRegex||(emojiRegex=new RegExp(_emojiRegex,"u")),emojiRegex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"emoji",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty());else if("uuid"===n.kind)uuidRegex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"uuid",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty());else if("nanoid"===n.kind)nanoidRegex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"nanoid",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty());else if("cuid"===n.kind)cuidRegex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"cuid",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty());else if("cuid2"===n.kind)cuid2Regex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"cuid2",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty());else if("ulid"===n.kind)ulidRegex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"ulid",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty());else if("url"===n.kind)try{new URL(e.data)}catch{r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"url",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty()}else if("regex"===n.kind){n.regex.lastIndex=0;n.regex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"regex",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty())}else if("trim"===n.kind)e.data=e.data.trim();else if("includes"===n.kind)e.data.includes(n.value,n.position)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:ZodIssueCode.invalid_string,validation:{includes:n.value,position:n.position},message:n.message}),t.dirty());else if("toLowerCase"===n.kind)e.data=e.data.toLowerCase();else if("toUpperCase"===n.kind)e.data=e.data.toUpperCase();else if("startsWith"===n.kind)e.data.startsWith(n.value)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:ZodIssueCode.invalid_string,validation:{startsWith:n.value},message:n.message}),t.dirty());else if("endsWith"===n.kind)e.data.endsWith(n.value)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:ZodIssueCode.invalid_string,validation:{endsWith:n.value},message:n.message}),t.dirty());else if("datetime"===n.kind){datetimeRegex(n).test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:ZodIssueCode.invalid_string,validation:"datetime",message:n.message}),t.dirty())}else if("date"===n.kind){dateRegex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:ZodIssueCode.invalid_string,validation:"date",message:n.message}),t.dirty())}else if("time"===n.kind){timeRegex(n).test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:ZodIssueCode.invalid_string,validation:"time",message:n.message}),t.dirty())}else"duration"===n.kind?durationRegex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"duration",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty()):"ip"===n.kind?isValidIP(e.data,n.version)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"ip",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty()):"jwt"===n.kind?isValidJWT(e.data,n.alg)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"jwt",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty()):"cidr"===n.kind?isValidCidr(e.data,n.version)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"cidr",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty()):"base64"===n.kind?base64Regex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"base64",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty()):"base64url"===n.kind?base64urlRegex.test(e.data)||(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{validation:"base64url",code:ZodIssueCode.invalid_string,message:n.message}),t.dirty()):util.assertNever(n);return{status:t.value,value:e.data}}_regex(e,t,r){return this.refinement((t=>e.test(t)),{validation:t,code:ZodIssueCode.invalid_string,...errorUtil.errToObj(r)})}_addCheck(e){return new ZodString({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...errorUtil.errToObj(e)})}url(e){return this._addCheck({kind:"url",...errorUtil.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...errorUtil.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...errorUtil.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...errorUtil.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...errorUtil.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...errorUtil.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...errorUtil.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...errorUtil.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...errorUtil.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...errorUtil.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...errorUtil.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...errorUtil.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,...errorUtil.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===e?.precision?null:e?.precision,...errorUtil.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...errorUtil.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...errorUtil.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...errorUtil.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...errorUtil.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...errorUtil.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...errorUtil.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...errorUtil.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...errorUtil.errToObj(t)})}nonempty(e){return this.min(1,errorUtil.errToObj(e))}trim(){return new ZodString({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new ZodString({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new ZodString({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find((e=>"datetime"===e.kind))}get isDate(){return!!this._def.checks.find((e=>"date"===e.kind))}get isTime(){return!!this._def.checks.find((e=>"time"===e.kind))}get isDuration(){return!!this._def.checks.find((e=>"duration"===e.kind))}get isEmail(){return!!this._def.checks.find((e=>"email"===e.kind))}get isURL(){return!!this._def.checks.find((e=>"url"===e.kind))}get isEmoji(){return!!this._def.checks.find((e=>"emoji"===e.kind))}get isUUID(){return!!this._def.checks.find((e=>"uuid"===e.kind))}get isNANOID(){return!!this._def.checks.find((e=>"nanoid"===e.kind))}get isCUID(){return!!this._def.checks.find((e=>"cuid"===e.kind))}get isCUID2(){return!!this._def.checks.find((e=>"cuid2"===e.kind))}get isULID(){return!!this._def.checks.find((e=>"ulid"===e.kind))}get isIP(){return!!this._def.checks.find((e=>"ip"===e.kind))}get isCIDR(){return!!this._def.checks.find((e=>"cidr"===e.kind))}get isBase64(){return!!this._def.checks.find((e=>"base64"===e.kind))}get isBase64url(){return!!this._def.checks.find((e=>"base64url"===e.kind))}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function floatSafeRemainder(e,t){const r=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,s=r>n?r:n;return Number.parseInt(e.toFixed(s).replace(".",""))%Number.parseInt(t.toFixed(s).replace(".",""))/10**s}ZodString.create=e=>new ZodString({checks:[],typeName:ZodFirstPartyTypeKind.ZodString,coerce:e?.coerce??!1,...processCreateParams(e)});class ZodNumber extends ZodType{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));if(this._getType(e)!==ZodParsedType.number){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.number,received:t.parsedType}),INVALID}let t;const r=new ParseStatus;for(const n of this._def.checks)if("int"===n.kind)util.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:"integer",received:"float",message:n.message}),r.dirty());else if("min"===n.kind){(n.inclusive?e.data<n.value:e.data<=n.value)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:ZodIssueCode.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty())}else if("max"===n.kind){(n.inclusive?e.data>n.value:e.data>=n.value)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:ZodIssueCode.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty())}else"multipleOf"===n.kind?0!==floatSafeRemainder(e.data,n.value)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:ZodIssueCode.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):"finite"===n.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:ZodIssueCode.not_finite,message:n.message}),r.dirty()):util.assertNever(n);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,errorUtil.toString(t))}gt(e,t){return this.setLimit("min",e,!1,errorUtil.toString(t))}lte(e,t){return this.setLimit("max",e,!0,errorUtil.toString(t))}lt(e,t){return this.setLimit("max",e,!1,errorUtil.toString(t))}setLimit(e,t,r,n){return new ZodNumber({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:errorUtil.toString(n)}]})}_addCheck(e){return new ZodNumber({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:errorUtil.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:errorUtil.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:errorUtil.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:errorUtil.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:errorUtil.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:errorUtil.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:errorUtil.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:errorUtil.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:errorUtil.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find((e=>"int"===e.kind||"multipleOf"===e.kind&&util.isInteger(e.value)))}get isFinite(){let e=null,t=null;for(const r of this._def.checks){if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value)}return Number.isFinite(t)&&Number.isFinite(e)}}ZodNumber.create=e=>new ZodNumber({checks:[],typeName:ZodFirstPartyTypeKind.ZodNumber,coerce:e?.coerce||!1,...processCreateParams(e)});class ZodBigInt extends ZodType{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==ZodParsedType.bigint)return this._getInvalidInput(e);let t;const r=new ParseStatus;for(const n of this._def.checks)if("min"===n.kind){(n.inclusive?e.data<n.value:e.data<=n.value)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:ZodIssueCode.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty())}else if("max"===n.kind){(n.inclusive?e.data>n.value:e.data>=n.value)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:ZodIssueCode.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty())}else"multipleOf"===n.kind?e.data%n.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),addIssueToContext(t,{code:ZodIssueCode.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):util.assertNever(n);return{status:r.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.bigint,received:t.parsedType}),INVALID}gte(e,t){return this.setLimit("min",e,!0,errorUtil.toString(t))}gt(e,t){return this.setLimit("min",e,!1,errorUtil.toString(t))}lte(e,t){return this.setLimit("max",e,!0,errorUtil.toString(t))}lt(e,t){return this.setLimit("max",e,!1,errorUtil.toString(t))}setLimit(e,t,r,n){return new ZodBigInt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:errorUtil.toString(n)}]})}_addCheck(e){return new ZodBigInt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:errorUtil.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:errorUtil.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:errorUtil.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:errorUtil.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:errorUtil.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}ZodBigInt.create=e=>new ZodBigInt({checks:[],typeName:ZodFirstPartyTypeKind.ZodBigInt,coerce:e?.coerce??!1,...processCreateParams(e)});class ZodBoolean extends ZodType{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==ZodParsedType.boolean){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.boolean,received:t.parsedType}),INVALID}return OK(e.data)}}ZodBoolean.create=e=>new ZodBoolean({typeName:ZodFirstPartyTypeKind.ZodBoolean,coerce:e?.coerce||!1,...processCreateParams(e)});class ZodDate extends ZodType{_parse(e){this._def.coerce&&(e.data=new Date(e.data));if(this._getType(e)!==ZodParsedType.date){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.date,received:t.parsedType}),INVALID}if(Number.isNaN(e.data.getTime())){return addIssueToContext(this._getOrReturnCtx(e),{code:ZodIssueCode.invalid_date}),INVALID}const t=new ParseStatus;let r;for(const n of this._def.checks)"min"===n.kind?e.data.getTime()<n.value&&(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:ZodIssueCode.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),t.dirty()):"max"===n.kind?e.data.getTime()>n.value&&(r=this._getOrReturnCtx(e,r),addIssueToContext(r,{code:ZodIssueCode.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),t.dirty()):util.assertNever(n);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ZodDate({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:errorUtil.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:errorUtil.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}ZodDate.create=e=>new ZodDate({checks:[],coerce:e?.coerce||!1,typeName:ZodFirstPartyTypeKind.ZodDate,...processCreateParams(e)});class ZodSymbol extends ZodType{_parse(e){if(this._getType(e)!==ZodParsedType.symbol){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.symbol,received:t.parsedType}),INVALID}return OK(e.data)}}ZodSymbol.create=e=>new ZodSymbol({typeName:ZodFirstPartyTypeKind.ZodSymbol,...processCreateParams(e)});class ZodUndefined extends ZodType{_parse(e){if(this._getType(e)!==ZodParsedType.undefined){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.undefined,received:t.parsedType}),INVALID}return OK(e.data)}}ZodUndefined.create=e=>new ZodUndefined({typeName:ZodFirstPartyTypeKind.ZodUndefined,...processCreateParams(e)});class ZodNull extends ZodType{_parse(e){if(this._getType(e)!==ZodParsedType.null){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.null,received:t.parsedType}),INVALID}return OK(e.data)}}ZodNull.create=e=>new ZodNull({typeName:ZodFirstPartyTypeKind.ZodNull,...processCreateParams(e)});class ZodAny extends ZodType{constructor(){super(...arguments),this._any=!0}_parse(e){return OK(e.data)}}ZodAny.create=e=>new ZodAny({typeName:ZodFirstPartyTypeKind.ZodAny,...processCreateParams(e)});class ZodUnknown extends ZodType{constructor(){super(...arguments),this._unknown=!0}_parse(e){return OK(e.data)}}ZodUnknown.create=e=>new ZodUnknown({typeName:ZodFirstPartyTypeKind.ZodUnknown,...processCreateParams(e)});class ZodNever extends ZodType{_parse(e){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.never,received:t.parsedType}),INVALID}}ZodNever.create=e=>new ZodNever({typeName:ZodFirstPartyTypeKind.ZodNever,...processCreateParams(e)});class ZodVoid extends ZodType{_parse(e){if(this._getType(e)!==ZodParsedType.undefined){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.void,received:t.parsedType}),INVALID}return OK(e.data)}}ZodVoid.create=e=>new ZodVoid({typeName:ZodFirstPartyTypeKind.ZodVoid,...processCreateParams(e)});class ZodArray extends ZodType{_parse(e){const{ctx:t,status:r}=this._processInputParams(e),n=this._def;if(t.parsedType!==ZodParsedType.array)return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.array,received:t.parsedType}),INVALID;if(null!==n.exactLength){const e=t.data.length>n.exactLength.value,s=t.data.length<n.exactLength.value;(e||s)&&(addIssueToContext(t,{code:e?ZodIssueCode.too_big:ZodIssueCode.too_small,minimum:s?n.exactLength.value:void 0,maximum:e?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),r.dirty())}if(null!==n.minLength&&t.data.length<n.minLength.value&&(addIssueToContext(t,{code:ZodIssueCode.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),r.dirty()),null!==n.maxLength&&t.data.length>n.maxLength.value&&(addIssueToContext(t,{code:ZodIssueCode.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map(((e,r)=>n.type._parseAsync(new ParseInputLazyPath(t,e,t.path,r))))).then((e=>ParseStatus.mergeArray(r,e)));const s=[...t.data].map(((e,r)=>n.type._parseSync(new ParseInputLazyPath(t,e,t.path,r))));return ParseStatus.mergeArray(r,s)}get element(){return this._def.type}min(e,t){return new ZodArray({...this._def,minLength:{value:e,message:errorUtil.toString(t)}})}max(e,t){return new ZodArray({...this._def,maxLength:{value:e,message:errorUtil.toString(t)}})}length(e,t){return new ZodArray({...this._def,exactLength:{value:e,message:errorUtil.toString(t)}})}nonempty(e){return this.min(1,e)}}function deepPartialify(e){if(e instanceof ZodObject){const t={};for(const r in e.shape){const n=e.shape[r];t[r]=ZodOptional.create(deepPartialify(n))}return new ZodObject({...e._def,shape:()=>t})}return e instanceof ZodArray?new ZodArray({...e._def,type:deepPartialify(e.element)}):e instanceof ZodOptional?ZodOptional.create(deepPartialify(e.unwrap())):e instanceof ZodNullable?ZodNullable.create(deepPartialify(e.unwrap())):e instanceof ZodTuple?ZodTuple.create(e.items.map((e=>deepPartialify(e)))):e}ZodArray.create=(e,t)=>new ZodArray({type:e,minLength:null,maxLength:null,exactLength:null,typeName:ZodFirstPartyTypeKind.ZodArray,...processCreateParams(t)});class ZodObject extends ZodType{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=util.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==ZodParsedType.object){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.object,received:t.parsedType}),INVALID}const{status:t,ctx:r}=this._processInputParams(e),{shape:n,keys:s}=this._getCached(),a=[];if(!(this._def.catchall instanceof ZodNever&&"strip"===this._def.unknownKeys))for(const e in r.data)s.includes(e)||a.push(e);const o=[];for(const e of s){const t=n[e],s=r.data[e];o.push({key:{status:"valid",value:e},value:t._parse(new ParseInputLazyPath(r,s,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof ZodNever){const e=this._def.unknownKeys;if("passthrough"===e)for(const e of a)o.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)a.length>0&&(addIssueToContext(r,{code:ZodIssueCode.unrecognized_keys,keys:a}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of a){const n=r.data[t];o.push({key:{status:"valid",value:t},value:e._parse(new ParseInputLazyPath(r,n,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then((async()=>{const e=[];for(const t of o){const r=await t.key,n=await t.value;e.push({key:r,value:n,alwaysSet:t.alwaysSet})}return e})).then((e=>ParseStatus.mergeObjectSync(t,e))):ParseStatus.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return errorUtil.errToObj,new ZodObject({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{const n=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:errorUtil.errToObj(e).message??n}:{message:n}}}:{}})}strip(){return new ZodObject({...this._def,unknownKeys:"strip"})}passthrough(){return new ZodObject({...this._def,unknownKeys:"passthrough"})}extend(e){return new ZodObject({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new ZodObject({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:ZodFirstPartyTypeKind.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new ZodObject({...this._def,catchall:e})}pick(e){const t={};for(const r of util.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new ZodObject({...this._def,shape:()=>t})}omit(e){const t={};for(const r of util.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new ZodObject({...this._def,shape:()=>t})}deepPartial(){return deepPartialify(this)}partial(e){const t={};for(const r of util.objectKeys(this.shape)){const n=this.shape[r];e&&!e[r]?t[r]=n:t[r]=n.optional()}return new ZodObject({...this._def,shape:()=>t})}required(e){const t={};for(const r of util.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof ZodOptional;)e=e._def.innerType;t[r]=e}return new ZodObject({...this._def,shape:()=>t})}keyof(){return createZodEnum(util.objectKeys(this.shape))}}ZodObject.create=(e,t)=>new ZodObject({shape:()=>e,unknownKeys:"strip",catchall:ZodNever.create(),typeName:ZodFirstPartyTypeKind.ZodObject,...processCreateParams(t)}),ZodObject.strictCreate=(e,t)=>new ZodObject({shape:()=>e,unknownKeys:"strict",catchall:ZodNever.create(),typeName:ZodFirstPartyTypeKind.ZodObject,...processCreateParams(t)}),ZodObject.lazycreate=(e,t)=>new ZodObject({shape:e,unknownKeys:"strip",catchall:ZodNever.create(),typeName:ZodFirstPartyTypeKind.ZodObject,...processCreateParams(t)});class ZodUnion extends ZodType{_parse(e){const{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map((async e=>{const r={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}}))).then((function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;const r=e.map((e=>new ZodError(e.ctx.common.issues)));return addIssueToContext(t,{code:ZodIssueCode.invalid_union,unionErrors:r}),INVALID}));{let e;const n=[];for(const s of r){const r={...t,common:{...t.common,issues:[]},parent:null},a=s._parseSync({data:t.data,path:t.path,parent:r});if("valid"===a.status)return a;"dirty"!==a.status||e||(e={result:a,ctx:r}),r.common.issues.length&&n.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const s=n.map((e=>new ZodError(e)));return addIssueToContext(t,{code:ZodIssueCode.invalid_union,unionErrors:s}),INVALID}}get options(){return this._def.options}}function mergeValues(e,t){const r=getParsedType(e),n=getParsedType(t);if(e===t)return{valid:!0,data:e};if(r===ZodParsedType.object&&n===ZodParsedType.object){const r=util.objectKeys(t),n=util.objectKeys(e).filter((e=>-1!==r.indexOf(e))),s={...e,...t};for(const r of n){const n=mergeValues(e[r],t[r]);if(!n.valid)return{valid:!1};s[r]=n.data}return{valid:!0,data:s}}if(r===ZodParsedType.array&&n===ZodParsedType.array){if(e.length!==t.length)return{valid:!1};const r=[];for(let n=0;n<e.length;n++){const s=mergeValues(e[n],t[n]);if(!s.valid)return{valid:!1};r.push(s.data)}return{valid:!0,data:r}}return r===ZodParsedType.date&&n===ZodParsedType.date&&+e===+t?{valid:!0,data:e}:{valid:!1}}ZodUnion.create=(e,t)=>new ZodUnion({options:e,typeName:ZodFirstPartyTypeKind.ZodUnion,...processCreateParams(t)});class ZodIntersection extends ZodType{_parse(e){const{status:t,ctx:r}=this._processInputParams(e),n=(e,n)=>{if(isAborted(e)||isAborted(n))return INVALID;const s=mergeValues(e.value,n.value);return s.valid?((isDirty(e)||isDirty(n))&&t.dirty(),{status:t.value,value:s.data}):(addIssueToContext(r,{code:ZodIssueCode.invalid_intersection_types}),INVALID)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then((([e,t])=>n(e,t))):n(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}ZodIntersection.create=(e,t,r)=>new ZodIntersection({left:e,right:t,typeName:ZodFirstPartyTypeKind.ZodIntersection,...processCreateParams(r)});class ZodTuple extends ZodType{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==ZodParsedType.array)return addIssueToContext(r,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.array,received:r.parsedType}),INVALID;if(r.data.length<this._def.items.length)return addIssueToContext(r,{code:ZodIssueCode.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),INVALID;!this._def.rest&&r.data.length>this._def.items.length&&(addIssueToContext(r,{code:ZodIssueCode.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const n=[...r.data].map(((e,t)=>{const n=this._def.items[t]||this._def.rest;return n?n._parse(new ParseInputLazyPath(r,e,r.path,t)):null})).filter((e=>!!e));return r.common.async?Promise.all(n).then((e=>ParseStatus.mergeArray(t,e))):ParseStatus.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new ZodTuple({...this._def,rest:e})}}ZodTuple.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new ZodTuple({items:e,typeName:ZodFirstPartyTypeKind.ZodTuple,rest:null,...processCreateParams(t)})};class ZodMap extends ZodType{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==ZodParsedType.map)return addIssueToContext(r,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.map,received:r.parsedType}),INVALID;const n=this._def.keyType,s=this._def.valueType,a=[...r.data.entries()].map((([e,t],a)=>({key:n._parse(new ParseInputLazyPath(r,e,r.path,[a,"key"])),value:s._parse(new ParseInputLazyPath(r,t,r.path,[a,"value"]))})));if(r.common.async){const e=new Map;return Promise.resolve().then((async()=>{for(const r of a){const n=await r.key,s=await r.value;if("aborted"===n.status||"aborted"===s.status)return INVALID;"dirty"!==n.status&&"dirty"!==s.status||t.dirty(),e.set(n.value,s.value)}return{status:t.value,value:e}}))}{const e=new Map;for(const r of a){const n=r.key,s=r.value;if("aborted"===n.status||"aborted"===s.status)return INVALID;"dirty"!==n.status&&"dirty"!==s.status||t.dirty(),e.set(n.value,s.value)}return{status:t.value,value:e}}}}ZodMap.create=(e,t,r)=>new ZodMap({valueType:t,keyType:e,typeName:ZodFirstPartyTypeKind.ZodMap,...processCreateParams(r)});class ZodSet extends ZodType{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==ZodParsedType.set)return addIssueToContext(r,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.set,received:r.parsedType}),INVALID;const n=this._def;null!==n.minSize&&r.data.size<n.minSize.value&&(addIssueToContext(r,{code:ZodIssueCode.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),null!==n.maxSize&&r.data.size>n.maxSize.value&&(addIssueToContext(r,{code:ZodIssueCode.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());const s=this._def.valueType;function a(e){const r=new Set;for(const n of e){if("aborted"===n.status)return INVALID;"dirty"===n.status&&t.dirty(),r.add(n.value)}return{status:t.value,value:r}}const o=[...r.data.values()].map(((e,t)=>s._parse(new ParseInputLazyPath(r,e,r.path,t))));return r.common.async?Promise.all(o).then((e=>a(e))):a(o)}min(e,t){return new ZodSet({...this._def,minSize:{value:e,message:errorUtil.toString(t)}})}max(e,t){return new ZodSet({...this._def,maxSize:{value:e,message:errorUtil.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}ZodSet.create=(e,t)=>new ZodSet({valueType:e,minSize:null,maxSize:null,typeName:ZodFirstPartyTypeKind.ZodSet,...processCreateParams(t)});class ZodLazy extends ZodType{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}ZodLazy.create=(e,t)=>new ZodLazy({getter:e,typeName:ZodFirstPartyTypeKind.ZodLazy,...processCreateParams(t)});class ZodLiteral extends ZodType{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{received:t.data,code:ZodIssueCode.invalid_literal,expected:this._def.value}),INVALID}return{status:"valid",value:e.data}}get value(){return this._def.value}}function createZodEnum(e,t){return new ZodEnum({values:e,typeName:ZodFirstPartyTypeKind.ZodEnum,...processCreateParams(t)})}ZodLiteral.create=(e,t)=>new ZodLiteral({value:e,typeName:ZodFirstPartyTypeKind.ZodLiteral,...processCreateParams(t)});class ZodEnum extends ZodType{_parse(e){if("string"!=typeof e.data){const t=this._getOrReturnCtx(e),r=this._def.values;return addIssueToContext(t,{expected:util.joinValues(r),received:t.parsedType,code:ZodIssueCode.invalid_type}),INVALID}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const t=this._getOrReturnCtx(e),r=this._def.values;return addIssueToContext(t,{received:t.data,code:ZodIssueCode.invalid_enum_value,options:r}),INVALID}return OK(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return ZodEnum.create(e,{...this._def,...t})}exclude(e,t=this._def){return ZodEnum.create(this.options.filter((t=>!e.includes(t))),{...this._def,...t})}}ZodEnum.create=createZodEnum;class ZodNativeEnum extends ZodType{_parse(e){const t=util.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==ZodParsedType.string&&r.parsedType!==ZodParsedType.number){const e=util.objectValues(t);return addIssueToContext(r,{expected:util.joinValues(e),received:r.parsedType,code:ZodIssueCode.invalid_type}),INVALID}if(this._cache||(this._cache=new Set(util.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const e=util.objectValues(t);return addIssueToContext(r,{received:r.data,code:ZodIssueCode.invalid_enum_value,options:e}),INVALID}return OK(e.data)}get enum(){return this._def.values}}ZodNativeEnum.create=(e,t)=>new ZodNativeEnum({values:e,typeName:ZodFirstPartyTypeKind.ZodNativeEnum,...processCreateParams(t)});class ZodPromise extends ZodType{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==ZodParsedType.promise&&!1===t.common.async)return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.promise,received:t.parsedType}),INVALID;const r=t.parsedType===ZodParsedType.promise?t.data:Promise.resolve(t.data);return OK(r.then((e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap}))))}}ZodPromise.create=(e,t)=>new ZodPromise({type:e,typeName:ZodFirstPartyTypeKind.ZodPromise,...processCreateParams(t)});class ZodEffects extends ZodType{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===ZodFirstPartyTypeKind.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:r}=this._processInputParams(e),n=this._def.effect||null,s={addIssue:e=>{addIssueToContext(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(s.addIssue=s.addIssue.bind(s),"preprocess"===n.type){const e=n.transform(r.data,s);if(r.common.async)return Promise.resolve(e).then((async e=>{if("aborted"===t.value)return INVALID;const n=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===n.status?INVALID:"dirty"===n.status||"dirty"===t.value?DIRTY(n.value):n}));{if("aborted"===t.value)return INVALID;const n=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===n.status?INVALID:"dirty"===n.status||"dirty"===t.value?DIRTY(n.value):n}}if("refinement"===n.type){const e=e=>{const t=n.refinement(e,s);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===r.common.async){const n=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===n.status?INVALID:("dirty"===n.status&&t.dirty(),e(n.value),{status:t.value,value:n.value})}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then((r=>"aborted"===r.status?INVALID:("dirty"===r.status&&t.dirty(),e(r.value).then((()=>({status:t.value,value:r.value}))))))}if("transform"===n.type){if(!1===r.common.async){const e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!isValid(e))return INVALID;const a=n.transform(e.value,s);if(a instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:a}}return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then((e=>isValid(e)?Promise.resolve(n.transform(e.value,s)).then((e=>({status:t.value,value:e}))):INVALID))}util.assertNever(n)}}ZodEffects.create=(e,t,r)=>new ZodEffects({schema:e,typeName:ZodFirstPartyTypeKind.ZodEffects,effect:t,...processCreateParams(r)}),ZodEffects.createWithPreprocess=(e,t,r)=>new ZodEffects({schema:t,effect:{type:"preprocess",transform:e},typeName:ZodFirstPartyTypeKind.ZodEffects,...processCreateParams(r)});class ZodOptional extends ZodType{_parse(e){return this._getType(e)===ZodParsedType.undefined?OK(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ZodOptional.create=(e,t)=>new ZodOptional({innerType:e,typeName:ZodFirstPartyTypeKind.ZodOptional,...processCreateParams(t)});class ZodNullable extends ZodType{_parse(e){return this._getType(e)===ZodParsedType.null?OK(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ZodNullable.create=(e,t)=>new ZodNullable({innerType:e,typeName:ZodFirstPartyTypeKind.ZodNullable,...processCreateParams(t)});class ZodDefault extends ZodType{_parse(e){const{ctx:t}=this._processInputParams(e);let r=t.data;return t.parsedType===ZodParsedType.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}ZodDefault.create=(e,t)=>new ZodDefault({innerType:e,typeName:ZodFirstPartyTypeKind.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...processCreateParams(t)});class ZodCatch extends ZodType{_parse(e){const{ctx:t}=this._processInputParams(e),r={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return isAsync(n)?n.then((e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new ZodError(r.common.issues)},input:r.data})}))):{status:"valid",value:"valid"===n.status?n.value:this._def.catchValue({get error(){return new ZodError(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}ZodCatch.create=(e,t)=>new ZodCatch({innerType:e,typeName:ZodFirstPartyTypeKind.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...processCreateParams(t)});class ZodNaN extends ZodType{_parse(e){if(this._getType(e)!==ZodParsedType.nan){const t=this._getOrReturnCtx(e);return addIssueToContext(t,{code:ZodIssueCode.invalid_type,expected:ZodParsedType.nan,received:t.parsedType}),INVALID}return{status:"valid",value:e.data}}}ZodNaN.create=e=>new ZodNaN({typeName:ZodFirstPartyTypeKind.ZodNaN,...processCreateParams(e)});class ZodBranded extends ZodType{_parse(e){const{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class ZodPipeline extends ZodType{_parse(e){const{status:t,ctx:r}=this._processInputParams(e);if(r.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?INVALID:"dirty"===e.status?(t.dirty(),DIRTY(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})()}{const e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?INVALID:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new ZodPipeline({in:e,out:t,typeName:ZodFirstPartyTypeKind.ZodPipeline})}}class ZodReadonly extends ZodType{_parse(e){const t=this._def.innerType._parse(e),r=e=>(isValid(e)&&(e.value=Object.freeze(e.value)),e);return isAsync(t)?t.then((e=>r(e))):r(t)}unwrap(){return this._def.innerType}}function cleanParams(e,t){const r="function"==typeof e?e(t):"string"==typeof e?{message:e}:e;return"string"==typeof r?{message:r}:r}function custom(e,t={},r){return e?ZodAny.create().superRefine(((n,s)=>{const a=e(n);if(a instanceof Promise)return a.then((e=>{if(!e){const e=cleanParams(t,n),a=e.fatal??r??!0;s.addIssue({code:"custom",...e,fatal:a})}}));if(!a){const e=cleanParams(t,n),a=e.fatal??r??!0;s.addIssue({code:"custom",...e,fatal:a})}})):ZodAny.create()}var ZodFirstPartyTypeKind;ZodReadonly.create=(e,t)=>new ZodReadonly({innerType:e,typeName:ZodFirstPartyTypeKind.ZodReadonly,...processCreateParams(t)}),function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(ZodFirstPartyTypeKind||(ZodFirstPartyTypeKind={})),ZodAny.create,ZodNever.create,ZodArray.create,ZodUnion.create,ZodIntersection.create,ZodTuple.create;const enumType=ZodEnum.create;ZodPromise.create,ZodOptional.create,ZodNullable.create;const ALGORITHMS={"SHA-256":"sha256-","SHA-384":"sha384-","SHA-512":"sha512-"},ALGORITHM_VALUES=Object.values(ALGORITHMS);enumType(Object.keys(ALGORITHMS)).optional().default("SHA-256"),custom((e=>"string"==typeof e&&ALGORITHM_VALUES.some((t=>e.startsWith(t)))));const ALLOWED_DIRECTIVES=["base-uri","child-src","connect-src","default-src","fenced-frame-src","font-src","form-action","frame-ancestors","frame-src","img-src","manifest-src","media-src","object-src","referrer","report-to","report-uri","require-trusted-types-for","sandbox","trusted-types","upgrade-insecure-requests","worker-src"];custom((e=>"string"==typeof e&&ALLOWED_DIRECTIVES.some((t=>e.startsWith(t)))));const ALGORITHM="AES-GCM";async function decodeKey(e){const t=decodeBase64(e);return crypto.subtle.importKey("raw",t,"AES-GCM",!0,["encrypt","decrypt"])}const encoder$1=new TextEncoder,decoder$1=new TextDecoder,IV_LENGTH=24;async function encryptString(e,t){const r=crypto.getRandomValues(new Uint8Array(12)),n=encoder$1.encode(t),s=await crypto.subtle.encrypt({name:"AES-GCM",iv:r},e,n);return encodeHexUpperCase(r)+encodeBase64(new Uint8Array(s))}async function decryptString(e,t){const r=decodeHex(t.slice(0,24)),n=decodeBase64(t.slice(24)),s=await crypto.subtle.decrypt({name:"AES-GCM",iv:r},e,n);return decoder$1.decode(s)}async function generateCspDigest(e,t){const r=await crypto.subtle.digest(t,encoder$1.encode(e)),n=encodeBase64(new Uint8Array(r));return`${ALGORITHMS[t]}${n}`}const renderTemplateResultSym=Symbol.for("astro.renderTemplateResult");class RenderTemplateResult{[renderTemplateResultSym]=!0;htmlParts;expressions;error;constructor(e,t){this.htmlParts=e,this.error=void 0,this.expressions=t.map((e=>isPromise(e)?Promise.resolve(e).catch((e=>{if(!this.error)throw this.error=e,e})):e))}render(e){const t=this.expressions.map((t=>createBufferedRenderer(e,(e=>{if(t||0===t)return renderChild(e,t)}))));let r=0;const n=()=>{for(;r<this.htmlParts.length;){const s=this.htmlParts[r],a=t[r];if(r++,s&&e.write(markHTMLString(s)),a){const e=a.flush();if(isPromise(e))return e.then(n)}}};return n()}}function isRenderTemplateResult(e){return"object"==typeof e&&null!==e&&!!e[renderTemplateResultSym]}function renderTemplate(e,...t){return new RenderTemplateResult(e,t)}const slotString=Symbol.for("astro:slot-string");class SlotString extends HTMLString{instructions;[slotString];constructor(e,t){super(e),this.instructions=t,this[slotString]=!0}}function isSlotString(e){return!!e[slotString]}function renderSlot(e,t,r){return!t&&r?renderSlot(e,r):{async render(r){await renderChild(r,"function"==typeof t?t(e):t)}}}async function renderSlotToString(e,t,r){let n="",s=null;const a={write(t){if(t instanceof SlotString)n+=t,t.instructions&&(s??=[],s.push(...t.instructions));else{if(t instanceof Response)return;"object"==typeof t&&"type"in t&&"string"==typeof t.type?(null===s&&(s=[]),s.push(t)):n+=chunkToString(e,t)}}},o=renderSlot(e,t,r);return await o.render(a),markHTMLString(new SlotString(n,s))}async function renderSlots(e,t={}){let r=null,n={};return t&&await Promise.all(Object.entries(t).map((([t,s])=>renderSlotToString(e,s).then((e=>{e.instructions&&(null===r&&(r=[]),r.push(...e.instructions)),n[t]=e}))))),{slotInstructions:r,children:n}}function createSlotValueFromString(e){return function(){return renderTemplate`${unescapeHTML(e)}`}}const internalProps=new Set(["server:component-path","server:component-export","server:component-directive","server:defer"]);function containsServerDirective(e){return"server:component-directive"in e}const SCRIPT_RE=/<\/script/giu,COMMENT_RE=/<!--/gu,SCRIPT_REPLACER="<\\/script",COMMENT_REPLACER="\\u003C!--";function safeJsonStringify(e){return JSON.stringify(e).replace(SCRIPT_RE,"<\\/script").replace(COMMENT_RE,"\\u003C!--")}function createSearchParams(e,t,r){const n=new URLSearchParams;return n.set("e",e),n.set("p",t),n.set("s",r),n}function isWithinURLLimit(e,t){return(e+"?"+t.toString()).length<2048}class ServerIslandComponent{result;props;slots;displayName;hostId;islandContent;componentPath;componentExport;componentId;constructor(e,t,r,n){this.result=e,this.props=t,this.slots=r,this.displayName=n}async init(){const e=await this.getIslandContent();if(this.result.cspDestination){this.result._metadata.extraScriptHashes.push(await generateCspDigest(SERVER_ISLAND_REPLACER,this.result.cspAlgorithm));const t=await generateCspDigest(e,this.result.cspAlgorithm);this.result._metadata.extraScriptHashes.push(t)}return createThinHead()}async render(e){const t=await this.getHostId(),r=await this.getIslandContent();e.write(createRenderInstruction({type:"server-island-runtime"})),e.write("\x3c!--[if astro]>server-island-start<![endif]--\x3e");for(const t in this.slots)"fallback"===t&&await renderChild(e,this.slots.fallback(this.result));e.write(`<script type="module" data-astro-rerun data-island-id="${t}">${r}<\/script>`)}getComponentPath(){if(this.componentPath)return this.componentPath;const e=this.props["server:component-path"];if(!e)throw new Error("Could not find server component path");return this.componentPath=e,e}getComponentExport(){if(this.componentExport)return this.componentExport;const e=this.props["server:component-export"];if(!e)throw new Error("Could not find server component export");return this.componentExport=e,e}async getHostId(){return this.hostId||(this.hostId=await crypto.randomUUID()),this.hostId}async getIslandContent(){if(this.islandContent)return this.islandContent;const e=this.getComponentPath(),t=this.getComponentExport(),r=this.result.serverIslandNameMap.get(e);if(!r)throw new Error("Could not find server component name");for(const e of Object.keys(this.props))internalProps.has(e)&&delete this.props[e];const n={};for(const e in this.slots)if("fallback"!==e){const t=await renderSlotToString(this.result,this.slots[e]);n[e]=t.toString()}const s=await this.result.key,a=0===Object.keys(this.props).length?"":await encryptString(s,JSON.stringify(this.props)),o=await this.getHostId(),i=this.result.base.endsWith("/")?"":"/";let d=`${this.result.base}${i}_server-islands/${r}${"always"===this.result.trailingSlash?"/":""}`;const c=createSearchParams(t,a,safeJsonStringify(n)),l=isWithinURLLimit(d,c);l&&(d+="?"+c.toString(),this.result._metadata.extraHead.push(markHTMLString(`<link rel="preload" as="fetch" href="${d}" crossorigin="anonymous">`)));const u=l?`let response = await fetch('${d}');`:`let data = {\n\tcomponentExport: ${safeJsonStringify(t)},\n\tencryptedProps: ${safeJsonStringify(a)},\n\tslots: ${safeJsonStringify(n)},\n};\nlet response = await fetch('${d}', {\n\tmethod: 'POST',\n\tbody: JSON.stringify(data),\n});`;return this.islandContent=`${u}replaceServerIsland('${o}', response);`,this.islandContent}}const renderServerIslandRuntime=()=>`<script>${SERVER_ISLAND_REPLACER}<\/script>`,SERVER_ISLAND_REPLACER=markHTMLString("async function replaceServerIsland(id, r) {\n\tlet s = document.querySelector(`script[data-island-id=\"${id}\"]`);\n\t// If there's no matching script, or the request fails then return\n\tif (!s || r.status !== 200 || r.headers.get('content-type')?.split(';')[0].trim() !== 'text/html') return;\n\t// Load the HTML before modifying the DOM in case of errors\n\tlet html = await r.text();\n\t// Remove any placeholder content before the island script\n\twhile (s.previousSibling && s.previousSibling.nodeType !== 8 && s.previousSibling.data !== '[if astro]>server-island-start<![endif]')\n\t\ts.previousSibling.remove();\n\ts.previousSibling?.remove();\n\t// Insert the new HTML\n\ts.before(document.createRange().createContextualFragment(html));\n\t// Remove the script. Prior to v5.4.2, this was the trick to force rerun of scripts.  Keeping it to minimize change to the existing behavior.\n\ts.remove();\n}".split("\n").map((e=>e.trim())).filter((e=>e&&!e.startsWith("//"))).join(" ")),Fragment=Symbol.for("astro:fragment"),Renderer=Symbol.for("astro:renderer"),encoder=new TextEncoder,decoder=new TextDecoder;function stringifyChunk(e,t){if(isRenderInstruction(t)){const r=t;switch(r.type){case"directive":{const{hydration:t}=r;let n=t&&determineIfNeedsHydrationScript(e),s=t&&determinesIfNeedsDirectiveScript(e,t.directive);if(n){let r=getPrescripts(e,"both",t.directive);return markHTMLString(r)}if(s){let r=getPrescripts(e,"directive",t.directive);return markHTMLString(r)}return""}case"head":return e._metadata.hasRenderedHead||e.partial?"":renderAllHeadContent(e);case"maybe-head":return e._metadata.hasRenderedHead||e._metadata.headInTree||e.partial?"":renderAllHeadContent(e);case"renderer-hydration-script":{const{rendererSpecificHydrationScripts:t}=e._metadata,{rendererName:n}=r;return t.has(n)?"":(t.add(n),r.render())}case"server-island-runtime":return e._metadata.hasRenderedServerIslandRuntime?"":(e._metadata.hasRenderedServerIslandRuntime=!0,renderServerIslandRuntime());default:throw new Error(`Unknown chunk type: ${t.type}`)}}else{if(t instanceof Response)return"";if(isSlotString(t)){let r="";const n=t;if(n.instructions)for(const t of n.instructions)r+=stringifyChunk(e,t);return r+=t.toString(),r}}return t.toString()}function chunkToString(e,t){return ArrayBuffer.isView(t)?decoder.decode(t):stringifyChunk(e,t)}function chunkToByteArray(e,t){if(ArrayBuffer.isView(t))return t;{const r=stringifyChunk(e,t);return encoder.encode(r.toString())}}function isRenderInstance(e){return!!e&&"object"==typeof e&&"render"in e&&"function"==typeof e.render}function renderChild(e,t){if(isPromise(t))return t.then((t=>renderChild(e,t)));if(t instanceof SlotString)e.write(t);else if(isHTMLString(t))e.write(t);else{if(Array.isArray(t))return renderArray(e,t);if("function"==typeof t)return renderChild(e,t());if(t||0===t)if("string"!=typeof t){if(isRenderInstance(t))return t.render(e);if(isRenderTemplateResult(t))return t.render(e);if(isAstroComponentInstance(t))return t.render(e);if(!ArrayBuffer.isView(t))return"object"==typeof t&&(Symbol.asyncIterator in t||Symbol.iterator in t)?Symbol.asyncIterator in t?renderAsyncIterable(e,t):renderIterable(e,t):void e.write(t);e.write(t)}else e.write(markHTMLString(escapeHTML(t)))}}function renderArray(e,t){const r=t.map((t=>createBufferedRenderer(e,(e=>renderChild(e,t)))))[Symbol.iterator](),n=()=>{for(;;){const{value:e,done:t}=r.next();if(t)break;const s=e.flush();if(isPromise(s))return s.then(n)}};return n()}function renderIterable(e,t){const r=t[Symbol.iterator](),n=()=>{for(;;){const{value:t,done:s}=r.next();if(s)break;const a=renderChild(e,t);if(isPromise(a))return a.then(n)}};return n()}async function renderAsyncIterable(e,t){for await(const r of t)await renderChild(e,r)}const astroComponentInstanceSym=Symbol.for("astro.componentInstance");class AstroComponentInstance{[astroComponentInstanceSym]=!0;result;props;slotValues;factory;returnValue;constructor(e,t,r,n){this.result=e,this.props=t,this.factory=n,this.slotValues={};for(const t in r){let n=!1,s=r[t](e);this.slotValues[t]=()=>n?r[t](e):(n=!0,s)}}init(e){return void 0!==this.returnValue||(this.returnValue=this.factory(e,this.props,this.slotValues),isPromise(this.returnValue)&&this.returnValue.then((e=>{this.returnValue=e})).catch((()=>{}))),this.returnValue}render(e){const t=this.init(this.result);return isPromise(t)?t.then((t=>this.renderImpl(e,t))):this.renderImpl(e,t)}renderImpl(e,t){return isHeadAndContent(t)?t.content.render(e):renderChild(e,t)}}function validateComponentProps(e,t,r){if(null!=e){const n=[...t.keys()].map((e=>`client:${e}`));for(const t of Object.keys(e))n.includes(t)&&console.warn(`You are attempting to render <${r} ${t} />, but ${r} is an Astro component. Astro components do not render in the client and should not have a hydration directive. Please use a framework component for client rendering.`)}}function createAstroComponentInstance(e,t,r,n,s={}){validateComponentProps(n,e.clientDirectives,t);const a=new AstroComponentInstance(e,n,s,r);return isAPropagatingComponent(e,r)&&e._metadata.propagators.add(a),a}function isAstroComponentInstance(e){return"object"==typeof e&&null!==e&&!!e[astroComponentInstanceSym]}const DOCTYPE_EXP=/<!doctype html/i;async function renderToString(e,t,r,n,s=!1,a){const o=await callComponentAsTemplateResultOrResponse(e,t,r,n,a);if(o instanceof Response)return o;let i="",d=!1;s&&await bufferHeadContent(e);const c={write(t){if(s&&!d&&(d=!0,!e.partial&&!DOCTYPE_EXP.test(String(t)))){const t=e.compressHTML?"<!DOCTYPE html>":"<!DOCTYPE html>\n";i+=t}t instanceof Response||(i+=chunkToString(e,t))}};return await o.render(c),i}async function renderToReadableStream(e,t,r,n,s=!1,a){const o=await callComponentAsTemplateResultOrResponse(e,t,r,n,a);if(o instanceof Response)return o;let i=!1;return s&&await bufferHeadContent(e),new ReadableStream({start(t){const r={write(r){if(s&&!i&&(i=!0,!e.partial&&!DOCTYPE_EXP.test(String(r)))){const r=e.compressHTML?"<!DOCTYPE html>":"<!DOCTYPE html>\n";t.enqueue(encoder.encode(r))}if(r instanceof Response)throw new AstroError({...ResponseSentError});const n=chunkToByteArray(e,r);t.enqueue(n)}};(async()=>{try{await o.render(r),t.close()}catch(e){AstroError.is(e)&&!e.loc&&e.setLocation({file:a?.component}),setTimeout((()=>t.error(e)),0)}})()},cancel(){e.cancelled=!0}})}async function callComponentAsTemplateResultOrResponse(e,t,r,n,s){const a=await t(e,r,n);if(a instanceof Response)return a;if(isHeadAndContent(a)){if(!isRenderTemplateResult(a.content))throw new AstroError({...OnlyResponseCanBeReturned,message:OnlyResponseCanBeReturned.message(s?.route,typeof a),location:{file:s?.component}});return a.content}if(!isRenderTemplateResult(a))throw new AstroError({...OnlyResponseCanBeReturned,message:OnlyResponseCanBeReturned.message(s?.route,typeof a),location:{file:s?.component}});return a}async function bufferHeadContent(e){const t=e._metadata.propagators.values();for(;;){const{value:r,done:n}=t.next();if(n)break;const s=await r.init(e);isHeadAndContent(s)&&s.head&&e._metadata.extraHead.push(s.head)}}async function renderToAsyncIterable(e,t,r,n,s=!1,a){const o=await callComponentAsTemplateResultOrResponse(e,t,r,n,a);if(o instanceof Response)return o;let i=!1;s&&await bufferHeadContent(e);let d=null,c=null;const l=[];let u=!1;const p={async next(){if(e.cancelled)return{done:!0,value:void 0};if(null!==c?await c.promise:u||l.length||(c=promiseWithResolvers(),await c.promise),u||(c=promiseWithResolvers()),d)throw d;let t=0;for(let e=0,r=l.length;e<r;e++)t+=l[e].length;let r=new Uint8Array(t),n=0;for(let e=0,t=l.length;e<t;e++){const t=l[e];r.set(t,n),n+=t.length}l.length=0;return{done:0===t&&u,value:r}},return:async()=>(e.cancelled=!0,{done:!0,value:void 0})},m={write(t){if(s&&!i&&(i=!0,!e.partial&&!DOCTYPE_EXP.test(String(t)))){const t=e.compressHTML?"<!DOCTYPE html>":"<!DOCTYPE html>\n";l.push(encoder.encode(t))}if(t instanceof Response)throw new AstroError(ResponseSentError);const r=chunkToByteArray(e,t);r.length>0?(l.push(r),c?.resolve()):l.length>0&&c?.resolve()}};return toPromise((()=>o.render(m))).catch((e=>{d=e})).finally((()=>{u=!0,c?.resolve()})),{[Symbol.asyncIterator]:()=>p}}function toPromise(e){try{const t=e();return isPromise(t)?t:Promise.resolve(t)}catch(e){return Promise.reject(e)}}function componentIsHTMLElement(e){return"undefined"!=typeof HTMLElement&&HTMLElement.isPrototypeOf(e)}async function renderHTMLElement(e,t,r,n){const s=getHTMLElementName(t);let a="";for(const e in r)a+=` ${e}="${toAttributeString(await r[e])}"`;return markHTMLString(`<${s}${a}>${await renderSlotToString(e,n?.default)}</${s}>`)}function getHTMLElementName(e){const t=customElements.getName(e);if(t)return t;return e.name.replace(/^HTML|Element$/g,"").replace(/[A-Z]/g,"-$&").toLowerCase().replace(/^-/,"html-")}const needsHeadRenderingSymbol=Symbol.for("astro.needsHeadRendering"),rendererAliases=new Map([["solid","solid-js"]]),clientOnlyValues=new Set(["solid-js","react","preact","vue","svelte"]);function guessRenderers(e){const t=e?.split(".").pop();switch(t){case"svelte":return["@astrojs/svelte"];case"vue":return["@astrojs/vue"];case"jsx":case"tsx":return["@astrojs/react","@astrojs/preact","@astrojs/solid-js","@astrojs/vue (jsx)"];default:return["@astrojs/react","@astrojs/preact","@astrojs/solid-js","@astrojs/vue","@astrojs/svelte"]}}function isFragmentComponent(e){return e===Fragment}function isHTMLComponent(e){return e&&!0===e["astro:html"]}const ASTRO_SLOT_EXP=/<\/?astro-slot\b[^>]*>/g,ASTRO_STATIC_SLOT_EXP=/<\/?astro-static-slot\b[^>]*>/g;function removeStaticAstroSlot(e,t=!0){const r=t?ASTRO_STATIC_SLOT_EXP:ASTRO_SLOT_EXP;return e.replace(r,"")}async function renderFrameworkComponent(e,t,r,n,s={}){if(!r&&"client:only"in n==!1)throw new Error(`Unable to render ${t} because it is ${r}!\nDid you forget to import the component or is it possible there is a typo?`);const{renderers:a,clientDirectives:o}=e,i={astroStaticSlot:!0,displayName:t},{hydration:d,isPage:c,props:l,propsWithoutTransitionAttributes:u}=extractDirectives(n,o);let p,m="";d&&(i.hydrate=d.directive,i.hydrateArgs=d.value,i.componentExport=d.componentExport,i.componentUrl=d.componentUrl);const h=guessRenderers(i.componentUrl),f=a.filter((e=>"astro:jsx"!==e.name)),{children:y,slotInstructions:g}=await renderSlots(e,s);let v;if("only"!==i.hydrate){let t=!1;try{t=r&&r[Renderer]}catch{}if(t){const e=r[Renderer];v=a.find((({name:t})=>t===e))}if(!v){let t;for(const n of a)try{if(await n.ssr.check.call({result:e},r,l,y)){v=n;break}}catch(e){t??=e}if(!v&&t)throw t}if(!v&&"function"==typeof HTMLElement&&componentIsHTMLElement(r)){const t=await renderHTMLElement(e,r,n,s);return{render(e){e.write(t)}}}}else{if(i.hydrateArgs){const e=rendererAliases.has(i.hydrateArgs)?rendererAliases.get(i.hydrateArgs):i.hydrateArgs;clientOnlyValues.has(e)&&(v=a.find((({name:t})=>t===`@astrojs/${e}`||t===e)))}if(v||1!==f.length||(v=f[0]),!v){const e=i.componentUrl?.split(".").pop();v=a.find((({name:t})=>t===`@astrojs/${e}`||t===e))}}if(v)"only"===i.hydrate?m=await renderSlotToString(e,s?.fallback):(performance.now(),({html:m,attrs:p}=await v.ssr.renderToStaticMarkup.call({result:e},r,u,y,i)));else{if("only"===i.hydrate){const e=rendererAliases.has(i.hydrateArgs)?rendererAliases.get(i.hydrateArgs):i.hydrateArgs;if(clientOnlyValues.has(e)){const e=f.length>1;throw new AstroError({...NoMatchingRenderer,message:NoMatchingRenderer.message(i.displayName,i?.componentUrl?.split(".").pop(),e,f.length),hint:NoMatchingRenderer.hint(formatList(h.map((e=>"`"+e+"`"))))})}throw new AstroError({...NoClientOnlyHint,message:NoClientOnlyHint.message(i.displayName),hint:NoClientOnlyHint.hint(h.map((e=>e.replace("@astrojs/",""))).join("|"))})}if("string"!=typeof r){const t=f.filter((e=>h.includes(e.name))),n=f.length>1;if(0===t.length)throw new AstroError({...NoMatchingRenderer,message:NoMatchingRenderer.message(i.displayName,i?.componentUrl?.split(".").pop(),n,f.length),hint:NoMatchingRenderer.hint(formatList(h.map((e=>"`"+e+"`"))))});if(1!==t.length)throw new Error(`Unable to render ${i.displayName}!\n\nThis component likely uses ${formatList(h)},\nbut Astro encountered an error during server-side rendering.\n\nPlease ensure that ${i.displayName}:\n1. Does not unconditionally access browser-specific globals like \`window\` or \`document\`.\n   If this is unavoidable, use the \`client:only\` hydration directive.\n2. Does not conditionally return \`null\` or \`undefined\` when rendered on the server.\n\nIf you're still stuck, please open an issue on GitHub or join us at https://astro.build/chat.`);v=t[0],({html:m,attrs:p}=await v.ssr.renderToStaticMarkup.call({result:e},r,u,y,i))}}if(!m&&"string"==typeof r){const t=sanitizeElementName(r),n=Object.values(y).join(""),s=renderTemplate`<${t}${internalSpreadAttributes(l,!0,t)}${markHTMLString(""===n&&voidElementNames.test(t)?"/>":`>${n}</${t}>`)}`;m="";const a={write(t){t instanceof Response||(m+=chunkToString(e,t))}};await s.render(a)}if(!d)return{render(e){if(g)for(const t of g)e.write(t);c||"astro:jsx"===v?.name?e.write(m):m&&m.length>0&&e.write(markHTMLString(removeStaticAstroSlot(m,v?.ssr?.supportsAstroStaticSlot)))}};const b=shorthash(`\x3c!--${i.componentExport.value}:${i.componentUrl}--\x3e\n${m}\n${serializeProps(l,i)}`),_=await generateHydrateScript({renderer:v,result:e,astroId:b,props:l,attrs:p},i);let T=[];if(m){if(Object.keys(y).length>0)for(const e of Object.keys(y)){let t=v?.ssr?.supportsAstroStaticSlot?i.hydrate?"astro-slot":"astro-static-slot":"astro-slot",r="default"===e?`<${t}>`:`<${t} name="${e}">`;m.includes(r)||T.push(e)}}else T=Object.keys(y);const C=T.length>0?T.map((e=>`<template data-astro-template${"default"!==e?`="${e}"`:""}>${y[e]}</template>`)).join(""):"";return _.children=`${m??""}${C}`,_.children&&(_.props["await-children"]="",_.children+="\x3c!--astro:end--\x3e"),{render(e){if(g)for(const t of g)e.write(t);e.write(createRenderInstruction({type:"directive",hydration:d})),"only"!==d.directive&&v?.ssr.renderHydrationScript&&e.write(createRenderInstruction({type:"renderer-hydration-script",rendererName:v.name,render:v.ssr.renderHydrationScript}));const t=renderElement$1("astro-island",_,!1);e.write(markHTMLString(t))}}}function sanitizeElementName(e){const t=/[&<>'"\s]+/;return t.test(e)?e.trim().split(t)[0].trim():e}async function renderFragmentComponent(e,t={}){const r=await renderSlotToString(e,t?.default);return{render(e){null!=r&&e.write(r)}}}async function renderHTMLComponent(e,t,r,n={}){const{slotInstructions:s,children:a}=await renderSlots(e,n),o=t({slots:a}),i=s?s.map((t=>chunkToString(e,t))).join(""):"";return{render(e){e.write(markHTMLString(i+o))}}}function renderAstroComponent(e,t,r,n,s={}){if(containsServerDirective(n)){const r=new ServerIslandComponent(e,n,s,t);return e._metadata.propagators.add(r),r}const a=createAstroComponentInstance(e,t,r,n,s);return{render:e=>a.render(e)}}function renderComponent(e,t,r,n,s={}){return isPromise(r)?r.catch(a).then((r=>renderComponent(e,t,r,n,s))):isFragmentComponent(r)?renderFragmentComponent(e,s).catch(a):(n=normalizeProps(n),isHTMLComponent(r)?renderHTMLComponent(e,r,n,s).catch(a):isAstroComponentFactory(r)?renderAstroComponent(e,t,r,n,s):renderFrameworkComponent(e,t,r,n,s).catch(a));function a(t){if(e.cancelled)return{render(){}};throw t}}function normalizeProps(e){if(void 0!==e["class:list"]){const t=e["class:list"];delete e["class:list"],e.class=clsx(e.class,t),""===e.class&&delete e.class}return e}async function renderComponentToString(e,t,r,n,s={},a=!1,o){let i="",d=!1,c="";a&&!e.partial&&nonAstroPageNeedsHeadInjection(r)&&(c+=chunkToString(e,maybeRenderHead()));try{const o={write(t){if(a&&!e.partial&&!d&&(d=!0,!/<!doctype html/i.test(String(t)))){const t=e.compressHTML?"<!DOCTYPE html>":"<!DOCTYPE html>\n";i+=t+c}t instanceof Response||(i+=chunkToString(e,t))}},l=await renderComponent(e,t,r,n,s);containsServerDirective(n)&&await bufferHeadContent(e),await l.render(o)}catch(e){throw AstroError.is(e)&&!e.loc&&e.setLocation({file:o?.component}),e}return i}function nonAstroPageNeedsHeadInjection(e){return!!e?.[needsHeadRenderingSymbol]}const ClientOnlyPlaceholder="astro-client-only",hasTriedRenderComponentSymbol=Symbol("hasTriedRenderComponent");async function renderJSX(e,t){switch(!0){case t instanceof HTMLString:return""===t.toString().trim()?"":t;case"string"==typeof t:return markHTMLString(escapeHTML(t));case"function"==typeof t:return t;case!t&&0!==t:return"";case Array.isArray(t):return markHTMLString((await Promise.all(t.map((t=>renderJSX(e,t))))).join(""))}return renderJSXVNode(e,t)}async function renderJSXVNode(e,t){if(isVNode(t)){switch(!0){case!t.type:throw new Error(`Unable to render ${e.pathname} because it contains an undefined Component!\nDid you forget to import the component or is it possible there is a typo?`);case t.type===Symbol.for("astro:fragment"):return renderJSX(e,t.props.children);case isAstroComponentFactory(t.type):{let r={},n={};for(const[s,a]of Object.entries(t.props??{}))"children"===s||a&&"object"==typeof a&&a.$$slot?n["children"===s?"default":s]=()=>renderJSX(e,a):r[s]=a;const s=await renderComponentToString(e,t.type.name,t.type,r,n);return markHTMLString(s)}case!t.type&&0!==t.type:return"";case"string"==typeof t.type&&"astro-client-only"!==t.type:return markHTMLString(await renderElement(e,t.type,t.props??{}))}if(t.type){let r=function(e){return Array.isArray(e)?e.map((e=>r(e))):isVNode(e)&&"slot"in e.props?(a[e.props.slot]=[...a[e.props.slot]??[],e],void delete e.props.slot):void a.default.push(e)};if("function"==typeof t.type&&t.props["server:root"]){const r=await t.type(t.props??{});return await renderJSX(e,r)}if("function"==typeof t.type){if(t.props[hasTriedRenderComponentSymbol]){delete t.props[hasTriedRenderComponentSymbol];const r=await t.type(t.props??{});return r?.[AstroJSX]||!r?await renderJSXVNode(e,r):void 0}t.props[hasTriedRenderComponentSymbol]=!0}const{children:n=null,...s}=t.props??{},a={default:[]};r(n);for(const[e,t]of Object.entries(s))t?.$$slot&&(a[e]=t,delete s[e]);const o=[],i={};for(const[t,r]of Object.entries(a))o.push(renderJSX(e,r).then((e=>{0!==e.toString().trim().length&&(i[t]=()=>e)})));let d;return await Promise.all(o),d="astro-client-only"===t.type&&t.props["client:only"]?await renderComponentToString(e,t.props["client:display-name"]??"",null,s,i):await renderComponentToString(e,"function"==typeof t.type?t.type.name:t.type,t.type,s,i),markHTMLString(d)}}return markHTMLString(`${t}`)}async function renderElement(e,t,{children:r,...n}){return markHTMLString(`<${t}${spreadAttributes(n)}${markHTMLString(null!=r&&""!=r||!voidElementNames.test(t)?`>${null==r?"":await renderJSX(e,prerenderElementChildren(t,r))}</${t}>`:"/>")}`)}function prerenderElementChildren(e,t){return"string"!=typeof t||"style"!==e&&"script"!==e?t:markHTMLString(t)}async function renderPage(e,t,r,n,s,a){if(!isAstroComponentFactory(t)){e._metadata.headInTree=e.componentMetadata.get(t.moduleId)?.containsHead??!1;const n={...r??{},"server:root":!0},s=await renderComponentToString(e,t.name,t,n,{},!0,a),o=encoder.encode(s),i=new Headers([["Content-Type","text/html"],["Content-Length",o.byteLength.toString()]]);return!e.shouldInjectCspMetaTags||"header"!==e.cspDestination&&"adapter"!==e.cspDestination||i.set("content-security-policy",renderCspContent(e)),new Response(o,{headers:i})}let o;if(e._metadata.headInTree=e.componentMetadata.get(t.moduleId)?.containsHead??!1,s)if(isNode&&!isDeno){o=await renderToAsyncIterable(e,t,r,n,!0,a)}else o=await renderToReadableStream(e,t,r,n,!0,a);else o=await renderToString(e,t,r,n,!0,a);if(o instanceof Response)return o;const i=e.response,d=new Headers(i.headers);(e.shouldInjectCspMetaTags&&"header"===e.cspDestination||"adapter"===e.cspDestination)&&d.set("content-security-policy",renderCspContent(e)),s||"string"!=typeof o||(o=encoder.encode(o),d.set("Content-Length",o.byteLength.toString()));let c=i.status,l=i.statusText;return"/404"===a?.route?(c=404,"OK"===l&&(l="Not Found")):"/500"===a?.route&&(c=500,"OK"===l&&(l="Internal Server Error")),c?new Response(o,{...i,headers:d,status:c,statusText:l}):new Response(o,{...i,headers:d})}async function renderScript(e,t){if(e._metadata.renderedScripts.has(t))return;e._metadata.renderedScripts.add(t);const r=e.inlinedScripts.get(t);if(null!=r)return r?markHTMLString(`<script type="module">${r}<\/script>`):"";const n=await e.resolve(t);return markHTMLString(`<script type="module" src="${e.userAssetsBase?("/"===e.base?"":e.base)+e.userAssetsBase:""}${n}"><\/script>`)}var cssesc_1,hasRequiredCssesc,commonjsGlobal="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function requireCssesc(){if(hasRequiredCssesc)return cssesc_1;hasRequiredCssesc=1;var e={}.hasOwnProperty,t=/[ -,\.\/:-@\[-\^`\{-~]/,r=/[ -,\.\/:-@\[\]\^`\{-~]/,n=/(^|\\+)?(\\[A-F0-9]{1,6})\x20(?![a-fA-F0-9\x20])/g,s=function s(a,o){"single"!=(o=function(t,r){if(!t)return r;var n={};for(var s in r)n[s]=e.call(t,s)?t[s]:r[s];return n}(o,s.options)).quotes&&"double"!=o.quotes&&(o.quotes="single");for(var i="double"==o.quotes?'"':"'",d=o.isIdentifier,c=a.charAt(0),l="",u=0,p=a.length;u<p;){var m=a.charAt(u++),h=m.charCodeAt(),f=void 0;if(h<32||h>126){if(h>=55296&&h<=56319&&u<p){var y=a.charCodeAt(u++);56320==(64512&y)?h=((1023&h)<<10)+(1023&y)+65536:u--}f="\\"+h.toString(16).toUpperCase()+" "}else f=o.escapeEverything?t.test(m)?"\\"+m:"\\"+h.toString(16).toUpperCase()+" ":/[\t\n\f\r\x0B]/.test(m)?"\\"+h.toString(16).toUpperCase()+" ":"\\"==m||!d&&('"'==m&&i==m||"'"==m&&i==m)||d&&r.test(m)?"\\"+m:m;l+=f}return d&&(/^-[-\d]/.test(l)?l="\\-"+l.slice(1):/\d/.test(c)&&(l="\\3"+c+" "+l.slice(1))),l=l.replace(n,(function(e,t,r){return t&&t.length%2?e:(t||"")+r})),!d&&o.wrap?i+l+i:l};return s.options={escapeEverything:!1,isIdentifier:!1,quotes:"single",wrap:!1},s.version="3.0.0",cssesc_1=s}function spreadAttributes(e={},t,{class:r}={}){let n="";r&&(void 0!==e.class?e.class+=` ${r}`:void 0!==e["class:list"]?e["class:list"]=[e["class:list"],r]:e.class=r);for(const[r,s]of Object.entries(e))n+=addAttribute(s,r,!0,t);return markHTMLString(n)}requireCssesc(),"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_".split("").reduce(((e,t)=>(e[t.charCodeAt(0)]=t,e)),[]),"-0123456789_".split("").reduce(((e,t)=>(e[t.charCodeAt(0)]=t,e)),[]);export{MiddlewareNotAResponse as $,AstroError as A,decryptString as B,createSlotValueFromString as C,DEFAULT_404_COMPONENT as D,isAstroComponentFactory as E,Fragment as F,i18nNoLocaleFoundInPath as G,ResponseSentError as H,originPathnameSymbol as I,RewriteWithBodyUsed as J,GetStaticPathsRequired as K,LocalsNotAnObject as L,InvalidGetStaticPathsReturn as M,NOOP_MIDDLEWARE_HEADER as N,InvalidGetStaticPathsEntry as O,GetStaticPathsExpectedParams as P,GetStaticPathsInvalidRouteParam as Q,REDIRECT_STATUS_CODES as R,PageNumberParamNotFound as S,NoMatchingStaticPathFound as T,PrerenderDynamicEndpointPathCollide as U,ReservedSlotName as V,renderSlotToString as W,renderJSX as X,chunkToString as Y,isRenderInstruction as Z,MiddlewareNoDataOrNextCalled as _,createComponent as a,SessionStorageInitError as a0,SessionStorageSaveError as a1,ForbiddenRewrite as a2,ASTRO_VERSION as a3,CspNotEnabled as a4,green as a5,LocalsReassigned as a6,generateCspDigest as a7,PrerenderClientAddressNotAvailable as a8,ClientAddressNotAvailable as a9,StaticClientAddressNotAvailable as aa,AstroResponseHeadersReassigned as ab,renderPage as ac,REWRITE_DIRECTIVE_HEADER_KEY as ad,REWRITE_DIRECTIVE_HEADER_VALUE as ae,renderEndpoint as af,ExpectedImage as ag,LocalImageUsedWrongly as ah,MissingImageDimension as ai,UnsupportedImageFormat as aj,IncompatibleDescriptorOptions as ak,UnsupportedImageConversion as al,toStyleString as am,NoImageMetadata as an,FailedToFetchRemoteImageDimensions as ao,ExpectedImageOptions as ap,ExpectedNotESMImage as aq,InvalidImageService as ar,ImageMissingAlt as as,ExperimentalFontsNotEnabled as at,FontFamilyNotFound as au,addAttribute as b,createAstro as c,renderTemplate as d,renderScript as e,renderSlot as f,decodeKey as g,renderHead as h,commonjsGlobal as i,ActionsReturnedInvalidDataError as j,escape as k,ROUTE_TYPE_HEADER as l,maybeRenderHead as m,REROUTE_DIRECTIVE_HEADER as n,ActionNotFoundError as o,bold as p,red as q,renderComponent as r,spreadAttributes as s,dim as t,unescapeHTML as u,blue as v,clientAddressSymbol as w,REROUTABLE_STATUS_CODES as x,yellow as y,responseSentSymbol as z};