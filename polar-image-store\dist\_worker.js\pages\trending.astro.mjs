globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,r as renderComponent,d as renderTemplate,m as maybeRenderHead}from"../chunks/astro/server_BgKLHZ62.mjs";import{$ as $$Layout}from"../chunks/Layout_CDS5so7g.mjs";import{$ as $$ProductCard}from"../chunks/ProductCard_BCRAEg7p.mjs";import{b as $$StructuredData}from"../chunks/StructuredData_DKf-6XMt.mjs";import{f as getTrendingProducts}from"../chunks/polar_maBmwXh0.mjs";export{renderers}from"../renderers.mjs";const $$Astro=createAstro("https://infpik.store"),prerender=!1,$$Trending=createComponent((async(e,r,t)=>{const a=e.createAstro($$Astro,r,t);a.self=$$Trending;let o=[],s=null;try{const e=a.locals?.runtime?.env;o=await getTrendingProducts(30,12,e)}catch(e){console.error("Error fetching trending products:",e),s="Failed to load trending products"}const n={items:[{name:"Home",url:"http://infpik.store"},{name:"Trending",url:"http://infpik.store/trending"}]};return renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:"Trending Products - InfPik",description:"Discover the most popular 3D Premium Icons based on recent sales. Updated daily to showcase what's trending right now.",canonical:"http://infpik.store/trending"},{default:async e=>renderTemplate`  ${renderComponent(e,"StructuredData",$$StructuredData,{type:"BreadcrumbList",data:n})} ${maybeRenderHead()}<div class="w-full px-4 md:px-8 py-8"> <section class="text-center mb-12"> <h1 class="text-4xl font-bold text-gray-900 mb-4">Trending Now</h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto">
Our top-selling 3D Premium Icons from the past week, loved by creators worldwide
</p> </section> ${s&&renderTemplate`<div class="bg-red-50 border border-red-200 rounded-xl p-6 mb-8"> <div class="flex items-center gap-3 text-red-800"> <svg class="w-6 h-6 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path> </svg> <div> <p class="font-semibold">⚠️ ${s}</p> <p class="text-sm">Please try again later.</p> </div> </div> </div>`} ${!s&&0===o.length&&renderTemplate`<div class="text-center py-16"> <p class="text-lg text-gray-600">No trending products available right now. Check back soon!</p> </div>`} ${!s&&o.length>0&&renderTemplate`<div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6"> ${o.slice(0,16).map((r=>renderTemplate`${renderComponent(e,"ProductCard",$$ProductCard,{product:r})}`))} </div>`} </div> `})}`}),"D:/code/image/polar-image-store/src/pages/trending.astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/trending.astro",$$url="/trending",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$Trending,file:$$file,prerender:false,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};