globalThis.process??={},globalThis.process.env??={};import{c as createPolarClient,t as transformPolarProduct}from"../../chunks/polar_maBmwXh0.mjs";export{renderers}from"../../renderers.mjs";const prerender=!1,GET=async({url:e,locals:r})=>{try{const t=e.searchParams.get("q"),s=r?.runtime?.env,o=createPolarClient(s),a=s?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca",n=await o.products.list({organizationId:a,isArchived:!1}),c=(n.result?.items||[]).map(transformPolarProduct).filter((e=>null!==e));if(!t||t.trim().length<2){const e=c.slice(0,10).map((e=>({id:e.id,name:e.name,description:e.description,slug:e.slug,image:e.images[0]||"",price:e.price,currency:e.currency,url:`/products/${e.slug}`})));return new Response(JSON.stringify({results:e,type:"products"}),{status:200,headers:{"Content-Type":"application/json","Cache-Control":"public, max-age=300"}})}const i=t.toLowerCase().trim(),l=c.filter((e=>{const r=e.name.toLowerCase().includes(i),t=e.description.toLowerCase().includes(i);return r||t}));console.log("Matching products:",l.slice(0,2));const p=l.map((e=>({id:e.id||"",name:e.name||"Unknown Product",description:e.description||"",slug:e.slug||"",image:e.images&&e.images[0]||"",price:e.price||0,currency:e.currency||"USD",url:`/products/${e.slug||"unknown"}`}))).sort(((e,r)=>{const t=e.name.toLowerCase()===i,s=r.name.toLowerCase()===i,o=e.name.toLowerCase().startsWith(i),a=r.name.toLowerCase().startsWith(i);return t&&!s?-1:!t&&s?1:o&&!a?-1:!o&&a?1:e.name.localeCompare(r.name)}));return new Response(JSON.stringify({results:p.slice(0,10),type:"products",total:p.length,query:t}),{status:200,headers:{"Content-Type":"application/json","Cache-Control":"public, max-age=60"}})}catch(e){return console.error("Search API error:",e),new Response(JSON.stringify({error:"Search failed"}),{status:500,headers:{"Content-Type":"application/json"}})}},_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};