globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,r as renderComponent,d as renderTemplate,m as maybeRenderHead}from"../../../chunks/astro/server_BgKLHZ62.mjs";import{$ as $$Layout}from"../../../chunks/Layout_CDS5so7g.mjs";import{$ as $$ProductCard}from"../../../chunks/ProductCard_BCRAEg7p.mjs";import{b as $$StructuredData}from"../../../chunks/StructuredData_DKf-6XMt.mjs";import{c as createPolarClient,t as transformPolarProduct,a as getProductsByCategory,b as getCategoryDisplayName}from"../../../chunks/polar_maBmwXh0.mjs";export{renderers}from"../../../renderers.mjs";const $$Astro=createAstro("https://infpik.store"),$$category=createComponent((async(e,t,r)=>{const o=e.createAstro($$Astro,t,r);o.self=$$category;const{category:a}=o.params;if(!a)return o.redirect("/products");let s=[],c=[],n=null;try{const e=o.locals?.runtime?.env,t=createPolarClient(e),r=e?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(r){const e=await t.products.list({organizationId:r,isArchived:!1});s=(e.result?.items||[]).map(transformPolarProduct).filter((e=>null!==e)),c=getProductsByCategory(s,a)}else;}catch(e){console.error("Error fetching products for category:",e),n="Failed to load products"}const l=getCategoryDisplayName(a),i={items:[{name:"Home",url:"http://infpik.store"},{name:"Products",url:"http://infpik.store/products"},{name:l,url:`http://infpik.store/products/category/${a}`}]};return renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:`${l} - InfPik`,description:`Browse our collection of ${l.toLowerCase()} 3D icons. High-quality 3D icons for creative projects.`,canonical:`http://infpik.store/products/category/${a}`},{default:async e=>renderTemplate`  ${renderComponent(e,"StructuredData",$$StructuredData,{type:"BreadcrumbList",data:i})} ${maybeRenderHead()}<div class="w-full px-4 md:px-8 py-8"> <section class="mb-12"> <h1 class="text-4xl font-bold text-gray-900 mb-4"> ${c.length>0?`Over ${c.length} ${l.toLowerCase()} 3D icons`:`No ${l.toLowerCase()} 3D icons found`} </h1> </section> ${0===c.length?renderTemplate`<div class="text-center py-16"> <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"> <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path> </svg> </div> <h3 class="text-2xl font-semibold text-gray-900 mb-4">No ${l} Found</h3> <p class="text-gray-600 mb-8">We don't have any ${l.toLowerCase()} items yet.</p> <a href="/products" class="inline-flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-accent-700 hover:scale-105"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path> </svg>
Browse All Products
</a> </div>`:renderTemplate`<div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6"> ${c.slice(0,16).map((t=>renderTemplate`${renderComponent(e,"ProductCard",$$ProductCard,{product:t})}`))} </div>`} </div> `})}`}),"D:/code/image/polar-image-store/src/pages/products/category/[category].astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/products/category/[category].astro",$$url="/products/category/[category]",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$category,file:$$file,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};