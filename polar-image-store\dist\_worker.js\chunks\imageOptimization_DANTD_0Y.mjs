function getProxiedS3Url(t){if(t.includes("polar-public-files.s3.amazonaws.com/")){const e=t.match(/polar-public-files\.s3\.amazonaws\.com\/(.+)/);if(e)return`/api/proxy/s3/${e[1]}`}return t}function getOptimizedImageUrl(t,e={}){if(t.startsWith("/")||t.includes("placeholder"))return t;const i=getProxiedS3Url(t),{width:r,height:a,quality:s=85,format:o="auto",fit:n="scale-down",sharpen:h,blur:p,saturation:u,brightness:m,contrast:g,gamma:l}=e,c=[];r&&c.push(`width=${r}`),a&&c.push(`height=${a}`),s&&c.push(`quality=${s}`),o&&c.push(`format=${o}`),n&&c.push(`fit=${n}`),h&&c.push(`sharpen=${h}`),p&&c.push(`blur=${p}`),u&&c.push(`saturation=${u}`),m&&c.push(`brightness=${m}`),g&&c.push(`contrast=${g}`),l&&c.push(`gamma=${l}`);return`/cdn-cgi/image/${c.join(",")}/${i}`}function getResponsiveImageUrls(t,e={}){const{sizes:i=[320,640,960,1280,1920],densities:r=[1,2],...a}=e,s=getProxiedS3Url(t),o=getOptimizedImageUrl(s,{...a,width:Math.max(...i)}),n=[];for(const t of i)for(const e of r){const i=t*e,r=getOptimizedImageUrl(s,{...a,width:i});n.push(`${r} ${i}w`)}return{src:o,srcset:n.join(", ")}}globalThis.process??={},globalThis.process.env??={};const ImagePresets={productCard:t=>getResponsiveImageUrls(getProxiedS3Url(t),{sizes:[320,480,640,800],densities:[1,2],width:800,height:600,quality:85,format:"auto",fit:"cover"}),productDetail:t=>getResponsiveImageUrls(getProxiedS3Url(t),{sizes:[400,600,800,1e3,1200],densities:[1,2],width:1e3,height:750,quality:90,format:"auto",fit:"contain"}),thumbnail:t=>getOptimizedImageUrl(getProxiedS3Url(t),{width:120,height:120,quality:85,format:"auto",fit:"cover"}),thumbnailLarge:t=>getOptimizedImageUrl(getProxiedS3Url(t),{width:200,height:200,quality:90,format:"auto",fit:"cover"}),hero:t=>getOptimizedImageUrl(getProxiedS3Url(t),{width:1920,height:1080,quality:90,format:"auto",fit:"cover"}),related:t=>getOptimizedImageUrl(getProxiedS3Url(t),{width:600,height:450,quality:85,format:"auto",fit:"cover"})};function generateSizesAttribute(t="general",e={}){let i;switch(t){case"productDetail":i={"(max-width: 1024px)":"100vw",...e};break;case"productCard":default:i={"(max-width: 640px)":"100vw","(max-width: 1024px)":"50vw","(max-width: 1280px)":"33vw",...e};break;case"thumbnail":i={"(max-width: 9999px)":"120px",...e};break;case"hero":i={"(max-width: 9999px)":"100vw",...e}}const r=Object.entries(i),a=r.slice(0,-1).map((([t,e])=>`${t} ${e}`)),s=r.length>0?r[r.length-1][1]:"50vw";return a.push(s),a.join(", ")}function supportsAVIF(t){if(!t)return!1;const e=t.match(/Chrome\/(\d+)/),i=t.match(/Firefox\/(\d+)/),r=t.match(/Version\/(\d+).*Safari/);return!!(e&&parseInt(e[1])>=85)||(!!(i&&parseInt(i[1])>=93)||!!(r&&parseInt(r[1])>=16))}function supportsWebP(t){if(!t)return!1;const e=t.match(/Chrome\/(\d+)/),i=t.match(/Firefox\/(\d+)/),r=t.match(/Version\/(\d+).*Safari/),a=t.match(/Edge\/(\d+)/);return!!(e&&parseInt(e[1])>=23)||(!!(i&&parseInt(i[1])>=65)||(!!(r&&parseInt(r[1])>=14)||!!(a&&parseInt(a[1])>=18)))}function getOptimalFormat(t){return supportsAVIF(t)?"avif":supportsWebP(t)?"webp":"jpeg"}export{ImagePresets,generateSizesAttribute,getOptimalFormat,getOptimizedImageUrl,getResponsiveImageUrls,supportsAVIF,supportsWebP};