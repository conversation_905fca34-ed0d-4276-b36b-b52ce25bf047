globalThis.process??={},globalThis.process.env??={};import{c as createPolarClient}from"../../chunks/polar_maBmwXh0.mjs";export{renderers}from"../../renderers.mjs";const prerender=!1,POST=async({request:e,locals:t})=>{try{const o=await e.json().catch((()=>({}))),r="string"==typeof o?.email?o.email:"";if(!r||!r.trim())return new Response(JSON.stringify({error:"Email is required"}),{status:400,headers:{"Content-Type":"application/json"}});const s=t?.runtime?.env,a=createPolarClient(s),i=s?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca",n=r.trim().toLowerCase();let l=[];try{const e=await a.customers.list({email:n,organizationId:i});l=e?.items??e?.result?.items??[]}catch(e){console.warn("customers.list exact email failed",e)}if(!l?.length)try{const e=await a.customers.list({query:n,organizationId:i});l=e?.items??e?.result?.items??[]}catch(e){console.warn("customers.list query fallback failed",e)}const c=(l||[]).find((e=>(e?.email||"").toLowerCase()===n))||null;if(!c?.id)return new Response(JSON.stringify({customer:{id:null,email:n,name:null,createdAt:null},orders:[],benefits:[],totalOrders:0,notFound:!0}),{status:200,headers:{"Content-Type":"application/json"}});let d=[];try{const e=await a.orders.list({organizationId:i,limit:100});d=(e?.items??e?.result?.items??[]).filter((e=>e?.customer?.id===c.id&&"paid"===(e?.status||"").toLowerCase()))}catch(e){console.warn("Failed to fetch orders:",e),d=[]}let m=[];try{const e=await a.customerSessions.create({customerId:c.id});if(e?.token){const t=`${"https://api.polar.sh"}/v1/customer-portal/downloadables?organization_id=${encodeURIComponent(i)}&limit=100`,o=await fetch(t,{method:"GET",headers:{"Customer-Session":e.token,Authorization:`Bearer ${e.token}`}});if(o.ok){const e=await o.json();m=(e?.items??e?.result?.items??[]).map((e=>({id:e.id,benefitId:e.benefit_id||e.benefitId,file:{id:e.file?.id,name:e.file?.name,size:e.file?.size,mimeType:e.file?.mime_type||e.file?.mimeType,download:e.file?.download,url:e.file?.download?.url}})))}else console.warn("Downloadables HTTP error",o.status)}}catch(e){console.warn("Failed to fetch downloadables:",e),m=[]}const u=d.map((e=>({id:e.id,amount:e.amount,taxAmount:e.taxAmount,currency:e.currency,createdAt:e.createdAt,status:e.status,products:e.products||[],checkoutId:e.checkoutId,customFieldValue:e.customFieldValue||{}})));return new Response(JSON.stringify({customer:{id:c.id,email:c.email,name:c.name,createdAt:c.createdAt},orders:u,benefits:m,totalOrders:d.length}),{status:200,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Customer orders API error:",e),new Response(JSON.stringify({error:"Failed to fetch customer orders"}),{status:500,headers:{"Content-Type":"application/json"}})}},_page=Object.freeze(Object.defineProperty({__proto__:null,POST:POST,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};