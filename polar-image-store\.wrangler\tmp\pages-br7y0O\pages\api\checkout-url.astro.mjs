globalThis.process??={},globalThis.process.env??={};import{c as createPolarClient}from"../../chunks/polar_maBmwXh0.mjs";export{renderers}from"../../renderers.mjs";const prerender=!1,POST=async({request:e,locals:r})=>{try{const{productId:t}=await e.json();if(!t||!t.trim())return new Response(JSON.stringify({error:"Product ID is required"}),{status:400,headers:{"Content-Type":"application/json"}});const s=r?.runtime?.env,o=createPolarClient(s);try{const e=await o.checkoutLinks.create({paymentProcessor:"stripe",productId:t.trim(),allowDiscountCodes:!0,requireBillingAddress:!1,successUrl:`${s?.PUBLIC_SITE_URL||"http://infpik.store"}/success`,embedOrigin:s?.PUBLIC_SITE_URL||"http://infpik.store"});return new Response(JSON.stringify({checkoutUrl:e.url,checkoutId:e.id}),{status:200,headers:{"Content-Type":"application/json"}})}catch(e){return console.error("Polar API error:",e),new Response(JSON.stringify({error:"Failed to create checkout link"}),{status:500,headers:{"Content-Type":"application/json"}})}}catch(e){return console.error("Checkout URL API error:",e),new Response(JSON.stringify({error:"Invalid request"}),{status:400,headers:{"Content-Type":"application/json"}})}},_page=Object.freeze(Object.defineProperty({__proto__:null,POST:POST,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};