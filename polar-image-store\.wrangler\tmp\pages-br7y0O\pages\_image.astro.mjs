globalThis.process??={},globalThis.process.env??={};import{g as getConfiguredImageService,i as imageConfig,a as isRemoteAllowed,l as lookup}from"../chunks/_astro_assets_BJ9yS0_G.mjs";import{i as isRemotePath}from"../chunks/path_h5kZAkfu.mjs";export{renderers}from"../renderers.mjs";const fnv1a52=e=>{const r=e.length;let t=0,o=0,a=8997,n=0,s=33826,i=0,g=40164,c=0,u=52210;for(;t<r;)a^=e.charCodeAt(t++),o=435*a,n=435*s,i=435*g,c=435*u,i+=a<<8,c+=s<<8,n+=o>>>16,a=65535&o,i+=n>>>16,s=65535&n,u=c+(i>>>16)&65535,g=65535&i;return 281474976710656*(15&u)+4294967296*g+65536*s+(a^u>>4)},etag=(e,r=!1)=>(r?'W/"':'"')+fnv1a52(e).toString(36)+e.length.toString(36)+'"';async function loadRemoteImage(e,r){try{const t=await fetch(e,{headers:r});if(!t.ok)return;return await t.arrayBuffer()}catch{return}}const GET=async({request:e})=>{try{const r=await getConfiguredImageService();if(!("transform"in r))throw new Error("Configured image service is not a local service");const t=new URL(e.url),o=await r.parseURL(t,imageConfig);if(!o?.src)throw new Error("Incorrect transform returned by `parseURL`");let a;const n=isRemotePath(o.src),s=n?new URL(o.src):new URL(o.src,t.origin);if(n&&!1===isRemoteAllowed(o.src,imageConfig))return new Response("Forbidden",{status:403});if(a=await loadRemoteImage(s,n?new Headers:e.headers),!a)return new Response("Not Found",{status:404});const{data:i,format:g}=await r.transform(new Uint8Array(a),o,imageConfig);return new Response(i,{status:200,headers:{"Content-Type":lookup(g)??`image/${g}`,"Cache-Control":"public, max-age=31536000",ETag:etag(i.toString()),Date:(new Date).toUTCString()}})}catch(e){return console.error("Could not process image request:",e),new Response(`Server Error: ${e}`,{status:500})}},_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};