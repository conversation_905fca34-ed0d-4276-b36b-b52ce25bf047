globalThis.process??={},globalThis.process.env??={};import{c as createPolarClient}from"../../chunks/polar_maBmwXh0.mjs";export{renderers}from"../../renderers.mjs";const prerender=!1,GET=async({url:e,locals:r})=>{try{const t=e.searchParams.get("email"),o=r?.runtime?.env,s=createPolarClient(o),a=o?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(t&&t.trim())try{const e=t.trim().toLowerCase();let r=[];try{const t=await s.customers.list({email:e});r=t?.items??t?.result?.items??[]}catch{}if(!r?.length)try{const t=await s.customers.list({query:e,organizationId:a});r=t?.items??t?.result?.items??[]}catch{}const o=(r||[]).find((r=>(r?.email||"").toLowerCase()===e))||null;if(o?.id){const e=await s.customerSessions.create({customerId:o.id});if(e.customerPortalUrl)return Response.redirect(e.customerPortalUrl,302)}}catch(e){console.error("Error creating customer session:",e)}try{const e=(await s.organizations.get({id:a})).slug;if(e){const r=`https://polar.sh/${e}/portal`;return Response.redirect(r,302)}}catch(e){console.error("Error fetching organization info:",e)}const c="https://polar.sh/portal";return Response.redirect(c,302)}catch(e){return console.error("Portal redirect error:",e),new Response(JSON.stringify({error:"Failed to redirect to customer portal"}),{status:500,headers:{"Content-Type":"application/json"}})}},_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};