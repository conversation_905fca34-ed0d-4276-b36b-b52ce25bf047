globalThis.process??={},globalThis.process.env??={};import{C as Checkout}from"../../chunks/index_Dn5Ya1F_.mjs";export{renderers}from"../../renderers.mjs";const prerender=!1,GET=Checkout({accessToken:"polar_oat_igptjJXuxXOyeTQmpqxDK5QHFnnU0JNNPS1Tc3eHD7u",successUrl:"http://infpik.store/success",server:"production"}),_page=Object.freeze(Object.defineProperty({__proto__:null,GET:GET,prerender:false},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};