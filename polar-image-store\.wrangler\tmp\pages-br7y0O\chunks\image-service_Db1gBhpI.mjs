globalThis.process??={},globalThis.process.env??={};import{i as isRemotePath,j as joinPaths}from"./path_h5kZAkfu.mjs";import{b as baseService,c as isESMImportedImage}from"./_astro_assets_BJ9yS0_G.mjs";import{i as commonjsGlobal}from"./astro/server_BgKLHZ62.mjs";import require$$0 from"node:crypto";var hasRequiredIsPlainObject,dist={},isPlainObject={};function requireIsPlainObject(){if(hasRequiredIsPlainObject)return isPlainObject;hasRequiredIsPlainObject=1,Object.defineProperty(isPlainObject,"__esModule",{value:!0});const e=Function.prototype.toString.call(Object);return isPlainObject.default=function(t){if("object"!=typeof t||null===t||"[object Object]"!==Object.prototype.toString.call(t))return!1;const r=Object.getPrototypeOf(t);return null===r||!!Object.prototype.hasOwnProperty.call(r,"constructor")&&("function"==typeof r.constructor&&r.constructor instanceof r.constructor&&Function.prototype.toString.call(r.constructor)===e)},isPlainObject}var hasRequiredBase64,hasRequiredEncoders,hasRequiredDist,encoders={},base64$1={exports:{}},base64=base64$1.exports;function requireBase64(){return hasRequiredBase64||(hasRequiredBase64=1,e=base64$1,t=base64$1.exports,function(r){var n=t,o=e&&e.exports==n&&e,i="object"==typeof commonjsGlobal&&commonjsGlobal;i.global!==i&&i.window!==i||(r=i);var a=function(e){this.message=e};(a.prototype=new Error).name="InvalidCharacterError";var s=function(e){throw new a(e)},c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",f=/[\t\n\f\r ]/g,u={encode:function(e){e=String(e),/[^\0-\xFF]/.test(e)&&s("The string to be encoded contains characters outside of the Latin1 range.");for(var t,r,n,o,i=e.length%3,a="",f=-1,u=e.length-i;++f<u;)t=e.charCodeAt(f)<<16,r=e.charCodeAt(++f)<<8,n=e.charCodeAt(++f),a+=c.charAt((o=t+r+n)>>18&63)+c.charAt(o>>12&63)+c.charAt(o>>6&63)+c.charAt(63&o);return 2==i?(t=e.charCodeAt(f)<<8,r=e.charCodeAt(++f),a+=c.charAt((o=t+r)>>10)+c.charAt(o>>4&63)+c.charAt(o<<2&63)+"="):1==i&&(o=e.charCodeAt(f),a+=c.charAt(o>>2)+c.charAt(o<<4&63)+"=="),a},decode:function(e){var t=(e=String(e).replace(f,"")).length;t%4==0&&(t=(e=e.replace(/==?$/,"")).length),(t%4==1||/[^+a-zA-Z0-9/]/.test(e))&&s("Invalid character: the string to be decoded is not correctly encoded.");for(var r,n,o=0,i="",a=-1;++a<t;)n=c.indexOf(e.charAt(a)),r=o%4?64*r+n:n,o++%4&&(i+=String.fromCharCode(255&r>>(-2*o&6)));return i},version:"1.0.0"};if(n&&!n.nodeType)if(o)o.exports=u;else for(var l in u)u.hasOwnProperty(l)&&(n[l]=u[l]);else r.base64=u}(base64)),base64$1.exports;var e,t}function requireEncoders(){if(hasRequiredEncoders)return encoders;hasRequiredEncoders=1,Object.defineProperty(encoders,"__esModule",{value:!0}),encoders.encoders=void 0;const e=requireBase64(),t=e=>{let t="";const r=new Uint8Array(e),n=r.byteLength;for(let e=0;e<n;e++){const n=r[e];n&&(t+=String.fromCharCode(n))}return t},r=r=>(0,e.encode)(t(r));return encoders.encoders={base64:r,base64url:e=>r(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,""),hex:e=>[...new Uint8Array(e)].map((e=>e.toString(16).padStart(2,"0"))).join(""),binary:t},encoders}function requireDist(){if(hasRequiredDist)return dist;hasRequiredDist=1;var e=dist&&dist.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(dist,"__esModule",{value:!0}),dist.deterministicString=void 0;const t=require$$0,r=e(requireIsPlainObject()),n=requireEncoders();function o(e){if("string"==typeof e)return JSON.stringify(e);if("symbol"==typeof e||"function"==typeof e)return e.toString();if("bigint"==typeof e)return`${e}n`;if(e===globalThis||null==e||"boolean"==typeof e||"number"==typeof e||"object"!=typeof e)return`${e}`;if(e instanceof Date)return`(${e.constructor.name}:${e.getTime()})`;if(e instanceof RegExp||e instanceof Error||e instanceof WeakMap||e instanceof WeakSet)return`(${e.constructor.name}:${e.toString()})`;if(e instanceof Set){let t=`(${e.constructor.name}:[`;for(const r of e.values())t+=`${o(r)},`;return t+="])",t}if(Array.isArray(e)||e instanceof Int8Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array||e instanceof BigInt64Array||e instanceof BigUint64Array){let t=`(${e.constructor.name}:[`;for(const[r,n]of e.entries())t+=`(${r}:${o(n)}),`;return t+="])",t}if(e instanceof ArrayBuffer||e instanceof SharedArrayBuffer){if(e.byteLength%8==0)return o(new BigUint64Array(e));if(e.byteLength%4==0)return o(new Uint32Array(e));if(e.byteLength%2==0)return o(new Uint16Array(e));{let t="(";for(let r=0;r<e.byteLength;r++)t+=`${o(new Uint8Array(e.slice(r,r+1)))},`;return t+=")",t}}if(e instanceof Map||(0,r.default)(e)){const t=[],r=e instanceof Map?e.entries():Object.entries(e);for(const[e,n]of r)t.push([o(e),o(n)]);if(!(e instanceof Map)){const r=Object.getOwnPropertySymbols(e);for(let n=0;n<r.length;n++)t.push([o(r[n]),o(e[r[n]])])}t.sort((([e],[t])=>e.localeCompare(t)));let n=`(${e.constructor.name}:[`;for(const[e,r]of t)n+=`(${e}:${r}),`;return n+="])",n}const t=[];for(const r in e)t.push([o(r),o(e[r])]);const n=Object.getOwnPropertySymbols(e);for(let r=0;r<n.length;r++)t.push([o(n[r]),o(e[n[r]])]);t.sort((([e],[t])=>e.localeCompare(t)));let i=`(${e.constructor.name}:[`;for(const[e,r]of t)i+=`(${e}:${r}),`;return i+="])",i}return dist.default=async function(e,r="SHA-1",i="hex"){const a=(new TextEncoder).encode(o(e)),s=await t.webcrypto.subtle.digest(r,a);return n.encoders[i](s)},dist.deterministicString=o,dist}function matchHostname(e,t,r){if(!t)return!0;if(!r||!t.startsWith("*"))return t===e.hostname;if(t.startsWith("**.")){const r=t.slice(2);return r!==e.hostname&&e.hostname.endsWith(r)}if(t.startsWith("*.")){const r=t.slice(1);return 1===e.hostname.replace(r,"").split(".").filter(Boolean).length}return!1}function matchPort(e,t){return!t||t===e.port}function matchProtocol(e,t){return!t||t===e.protocol.slice(0,-1)}function matchPathname(e,t,r){if(!t)return!0;if(!t.endsWith("*"))return t===e.pathname;if(t.endsWith("/**")){const r=t.slice(0,-2);return r!==e.pathname&&e.pathname.startsWith(r)}if(t.endsWith("/*")){const r=t.slice(0,-1);return 1===e.pathname.replace(r,"").split("/").filter(Boolean).length}return!1}function matchPattern(e,t){return matchProtocol(e,t.protocol)&&matchHostname(e,t.hostname,!0)&&matchPort(e,t.port)&&matchPathname(e,t.pathname)}function isRemoteAllowed(e,{domains:t=[],remotePatterns:r=[]}){if(!isRemotePath(e))return!1;const n=new URL(e);return t.some((e=>matchHostname(n,e)))||r.some((e=>matchPattern(n,e)))}requireDist();const __vite_import_meta_env__={ASSETS_PREFIX:void 0,BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,PUBLIC_SITE_URL:"http://infpik.store",SITE:"https://infpik.store",SSR:!0},service={...baseService,getURL:(e,t)=>{const r=["onerror=redirect"];e.width&&r.push(`width=${e.width}`),e.height&&r.push(`height=${e.height}`),e.quality&&r.push(`quality=${e.quality}`),e.fit&&r.push(`fit=${e.fit}`),e.format&&r.push(`format=${e.format}`);let n="";if(isESMImportedImage(e.src))n=e.src.src;else{if(!isRemoteAllowed(e.src,t))return e.src;n=e.src}return joinPaths(Object.assign(__vite_import_meta_env__,{Path:process.env.Path}).BASE_URL,"/cdn-cgi/image",r.join(","),n)}};var image_service_default=service;export{image_service_default as default};